{"name": "entrep-client", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"start": "electron .", "test": "live-server .", "packager": "electron-packager ./ entrep-client win32 x84 --out ./../build --overwrite --icon=./src/favicon.ico"}, "author": "", "license": "ISC", "dependencies": {"angular": "^1.6.6", "angular-animate": "^1.6.6", "angular-aria": "^1.6.6", "angular-hammer": "^2.2.0", "angular-material": "^1.1.5", "angular-messages": "^1.6.6", "angular-resource": "^1.6.6", "angular-ui-bootstrap": "^2.5.6", "angular-virtual-keyboard": "^0.4.3", "bootstrap": "^3.3.7", "echarts": "^5.3.3", "electron-json-storage": "^4.5.0", "jquery": "^3.2.1", "ngstorage": "^0.3.11"}, "devDependencies": {"electron": "7.3.3"}}