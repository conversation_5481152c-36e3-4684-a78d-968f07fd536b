'use strict';

// Define the `entrepreneurApp` module
angular
    .module('entrepApp', ['ngMaterial', 'ngStorage', 'entrepApp.core', 
      'entrepApp.login', 'entrepApp.register', 
      'entrepApp.info', 'entrepApp.finance', 'entrepApp.logistic',
      'entrepApp.market', 'entrepApp.produce'
    ])
    // configure the app
    .config(['$httpProvider', '$mdThemingProvider', '$mdIconProvider', AppConfigProvider])
    // define the controller
    .controller('AppController', ['appCache', 'UserService', '$log', AppController])
    // define the main controller
    .controller('MainController', ['appCache', 'DiskCacheService', 'UserService', '$log', MainController]);

// the function to configure the application
function AppConfigProvider($httpProvider, $mdThemingProvider, $mdIconProvider) {
  $mdIconProvider.defaultIconSet('assets/mdi.svg')
  // setup the theme
  $mdThemingProvider.theme('default')
      .primaryPalette('brown')
      .accentPalette('red');

  $httpProvider.interceptors.push(['$q', '$localStorage', 'appCache', 
    function ($q, $localStorage, appCache) {
      return {
        'request': function (config) {
          config.headers = config.headers || {};
          if (appCache.token) {
            config.headers.Authorization = 'Bearer ' + appCache.token;
          }
          return config;
        },
        'responseError': function (response) {
          if (response.status === 401 || response.status === 403 && response.data.error!=='Can not register') {
            delete appCache.token;
            delete $localStorage.user;
          }
          return $q.reject(response);
        }
      };
    }
  ]);
}

// the app controller
function AppController(appCache, UserService, $log) {
  var self = this;

  // refrence the app cache
  self.appCache = appCache;
}

// the main controller
function MainController(appCache, DiskCacheService, UserService, $log) {
  var self = this;

  self.userLoading = true;
  // refrence the app cache
  self.appCache = appCache;

  UserService.getUser(function() {
    self.userLoading = false;
    //$log.debug(JSON.stringify(self.appCache));
  }, function(e) {
    if (DiskCacheService.isSupported()) {
      DiskCacheService.clear();
    } else {
      delete $localStorage.user.token;  
    }
      
    delete appCache.token;
    
    $log.error(e.data);
  });
}