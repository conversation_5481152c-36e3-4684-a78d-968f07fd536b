'use strict';

angular
    .module('entrepApp.core')
    .service('AuthService', ['appCache', '$localStorage', '$http', '$rootScope', '$window', 'appConfig', 'DiskCacheService', AuthService]);

// Define the auth service
function AuthService(appCache, $localStorage, $http, $rootScope, $window, appConfig, DiskCacheService) {
  var self = this;

  self.login = login;
  self.logout = logout;
 

  if (DiskCacheService.isSupported()) {
    DiskCacheService.load(function(disCache) {
      angular.extend(appCache, disCache.account);
      angular.extend(appConfig, disCache.config);
      appCache.loading = false;
      $rootScope.$apply();
    });
  } else {
    // load the local storage
    if ($localStorage.user) {
      angular.extend(appCache, $localStorage.user);
      angular.extend(appConfig, $localStorage.config);
    }
    appCache.loading = false;
  }
  appCache.loading = false;

  // login
  function login(credentials, success, error) {
    var req = {
      method: 'POST',
      url: appConfig.serverUriBase + '/user/login',
      data: credentials
    }
    $http(req).then(function(res) {
      angular.extend(appCache, res.data);
      if (res.data.registerRequired) {
        appCache.user.status = 'New';
      } else {
        appCache.user.status = 'Operation';
      }
      var cacheAccount = {};
      angular.extend(cacheAccount, res.data);
      delete cacheAccount.registerRequired;
      if (DiskCacheService.isSupported()) {
        DiskCacheService.saveAccount(cacheAccount);
        DiskCacheService.saveConfig(appConfig);
      } else {
        $localStorage.user = cacheAccount;
        $localStorage.config = appConfig;
      }
      success();
    }).catch(function(e) {
      error(e);
    });
  }

  // logout
  function logout() {
    if (DiskCacheService.isSupported()) {
      DiskCacheService.removeAccount();
    } else {
      delete $localStorage.user.token;  
    }

    delete appCache.token;
    $window.location.reload();
  }
}