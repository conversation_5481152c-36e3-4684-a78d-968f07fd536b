'use strict';

// Define the `core` module
angular
    .module('entrepApp.core', ['ngStorage', 'hmTouchEvents'])
    // define the app cache
    .value('appCache', { loading: true, system: { codes: {} }, user: {}, actions: [], 
      calculatedParams: {
        showCash: 0,
        availableLoan: 0,
        shortLoans: [],
        longLoans: [],
        receivables: [],
        productOrders: [],
        productDevelops: [],
        marketDevelops: [],
        isoDevelops: [],
        materialOrders: [],
        materialsInventory: [],
        productsInventory: [],
        workshops: []
      },
      moveState: {
        shortLoans: [],
        longLoans: [],
        receivables: [],
        materialOrders: [],
        workshops: [],
        products: [],
        deps: [],
      }
    })
    // the config
    .value('appConfig', { serverUriBase: 'http://192.168.2.150:1024/api/v1' } )
    // define the user cache
    .constant('USER_ACTIONS', [
      {
        id: 'Year_Meeting',
        name: '新年规划'
      },
      {
        id: 'Pay_Tax',
        name: '支付应付税'
      },
      {
        id: 'Pay_Long_Loan_Interest',
        name: '支付长贷利息'
      },
      {
        id: 'Update_Long_Loan',
        name: '更新长贷'
      },
      {
        id: 'Pay_AD',
        name: '投放广告'
      },
      {
        id: 'Attend_Order',
        name: '参加订货会'
      },
      {
        id: 'Long_Loan',
        name: '申请长贷'
      },
      {
        id: 'Update_Short_Loan',
        name: '更新短贷'
      },
      {
        id: 'Update_Material',
        name: '更新原料库'
      },
      {
        id: 'Update_Produce',
        name: '更新生产'
      },
      {
        id: 'Short_Loan',
        name: '申请短贷'
      },
      {
        id: 'Buy_Material',
        name: '下原料订单'
      },
      {
        id: 'Buy_Rent_Workshop',
        name: '购置厂房'
      },
      {
        id: 'Invest_Product_Line',
        name: '生产线投资'
      },
      {
        id: 'New_Transfer',
        name: '生产线转产'
      },
      {
        id: 'Sell_Product_Line',
        name: '变卖生产线'
      },
      {
        id: 'New_Produce',
        name: '下一批生产'
      },
      {
        id: 'Develop_Product',
        name: '产品研发'
      },
      {
        id: 'Workshop_Process',
        name: '厂房处理'
      },
      {
        id: 'Develop_Market',
        name: '新市场开拓'
      },
      {
        id: 'Develop_ISO',
        name: 'ISO投资'
      },
      {
        id: 'Update_Receivable',
        name: '应收款更新'
      },
      {
        id: 'Sell_Product',
        name: '按订单交货'
      },
      {
        id: 'Pay_Dep',
        name: '计提折旧'
      },
      {
        id: 'Pay_Maintenance',
        name: '支付维护费'
      },
      {
        id: 'Pay_Rent',
        name: '支付厂房租金'
      },
      {
        id: 'Sell_Inventory',
        name: '出售库存'
      },
      {
        id: 'Workshop_Discount',
        name: '厂房贴现'
      },
      {
        id: 'Emergency_Buy',
        name: '紧急采购'
      },
      {
        id: 'Discount',
        name: '应收款贴现'
      } 
    ]
  );