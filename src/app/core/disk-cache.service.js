'use strict';

angular
    .module('entrepApp.core')
    .service('DiskCacheService', ['$log', DiskCacheService]);

// Define the disk cache sevice
function DiskCacheService($log) {
  var self = this;

  self.isSupported = isSupported;
  self.load = load;
  self.clear = clear;
  self.saveAccount = saveAccount;
  self.saveConfig = saveConfig;
  self.removeAccount = removeAccount

  var jsonStorage = null;
  if (typeof require === 'function') {
    jsonStorage = require('electron-json-storage');
  }

  function isSupported() {
    return (jsonStorage != null);
  }

  // load cache files from disk
  function load(callback) {
    if (!jsonStorage) {
      callback({});
      return;
    }

    // the disk loaded files
    var diskCache = {};
    // load account file
    jsonStorage.get('account', { dataPath: 'cache' }, function(e, data) {
      if (!e) {
        diskCache.account = data;
      } else {
        diskCache.account = {};
        $log.error(e.data);
      }
      _handleDiskCache(diskCache, callback);
    });
    // load config file
    jsonStorage.get('config', { dataPath: 'cache' }, function(e, data) {
      if (!e) {
        diskCache.config = data;
      } else {
        diskCache.config = {};
        $log.error(e.data);
      }
      _handleDiskCache(diskCache, callback);
    });
  }

  // clear the cache
  function clear() {
    if (!jsonStorage) {
      return;
    }
    
    jsonStorage.clear({ dataPath: 'cache' });
  }

  // remove account 
  function removeAccount() {
    if (!jsonStorage) {
      return;
    }
    jsonStorage.remove('account', { dataPath: 'cache' });

  }
  // save account file
  function saveAccount(account) {
    if (!jsonStorage) {
      return;
    }

    jsonStorage.set('account', account, { dataPath: 'cache' });
  }

  // save config file
  function saveConfig(config) {
    if (!jsonStorage) {
      return;
    }

    jsonStorage.set('config', config, { dataPath: 'cache' });
  }

  // handle the disk cache
  function _handleDiskCache(diskCache, callback) {
    if (diskCache.account && diskCache.config) {
      callback(diskCache);
    }
  }
}