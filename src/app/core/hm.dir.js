'use strict';

angular
    .module('entrepApp.core')
    .directive('hmDir', ['$log', function ($log) {
      return {
        restrict: 'AE',
        transclude: true,
        scope: {},
        template: '<div hm-panstart="onHMPanStart($event)" hm-panmove="onHMPanMove($event)"' + 
                  ' hm-panend="onHMPanEnd()" hm-rotate="onHMRotate($event)" hm-pinch="onHMPinch($event)"' + 
                  ' ng-transclude ></div>',
        link: function (scope, element, attrs) {
          var rotateMatrix = angular.isDefined(attrs.rotateMatrix) ? scope.$eval(attrs.rotateMatrix) : null;
          var initSize = scope.$eval(attrs.initSize);
          var initRotate = scope.$eval(attrs.initRotate) || 0;
          var startX = 0, startY = 0, x = 0, y = 0;
          var startScale = 1;
          var currentScale = 1;
          var startRotation = 0;
          var currentRotation = initRotate;
          var currentPanTarget = null;

          element.css({
            position: 'absolute',
            cursor: 'pointer'
          });
          element.children().css({
            width: initSize[0] + 'px',
            height: initSize[1]=='auto'?'auto':initSize[1] + 'px'
          });
          element.children().css({transform: 'rotate(' + initRotate + 'deg)'});

          function checkNoMove(elem) {
            if (elem.getAttribute('data-no-move')) {
              return true;
            }
            var curElement = elem.parentNode;
            while (curElement && curElement != document) {
              if (curElement.getAttribute('data-no-move')) {
                return true;
              } 
              curElement = curElement.parentNode;
            }
            return false;
          }

          scope.onHMPanStart = function(event) {
            if (checkNoMove(event.target)) {
              return;
            }
            startX = event.center.x - x;
            startY = event.center.y - y;
            currentPanTarget = event.target;
          }
          scope.onHMPanMove = function(event) {
            if (!currentPanTarget) {
              return;
            }

            y = event.center.y - startY;
            x = event.center.x - startX;
            var tx = x;
            var ty = y;
            if (rotateMatrix) {
              x = tx * rotateMatrix[0] + ty * rotateMatrix[1];
              y = tx * rotateMatrix[2] + ty * rotateMatrix[3]; 
            }
            element.css({
              top: y + 'px',
              left:  x + 'px'
            });
            x = tx;
            y = ty;
          } 
          scope.onHMPanEnd = function() {
            currentPanTarget = null;
          }

          scope.onHMPinch = function(event) {
            //$log.info(event.type + ' ' + event.eventType);

            if (event.eventType == 1) {
              startScale = currentScale;
            } else if (event.eventType == 2) {
              var scale = event.scale * startScale;
              currentScale = scale > 1 ? (scale > 3 ? 3 : scale) : 1;
              element.children().css({
                width: Math.floor(initSize[0] * currentScale) + 'px',
                height: Math.floor(initSize[1] * currentScale) + 'px'
              });
            }
          }

          scope.onHMRotate = function(event) {
            //$log.info(event.type + ' ' + event.eventType);

            if (event.eventType == 1) {
              initRotate = currentRotation;
              startRotation = event.rotation;
            } else if (event.eventType == 2) {
              currentRotation = initRotate + (event.rotation - startRotation);
              element.children().css({transform: 'rotate(' + currentRotation + 'deg)'});
            } else if (event.eventType == 4) {
              currentRotation = Math.round(currentRotation / 90) * 90;
              element.children().css({transform: 'rotate(' + currentRotation + 'deg)'});
            }
          }
        }  
      }
    }]);