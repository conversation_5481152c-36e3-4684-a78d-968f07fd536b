'use strict';

angular
    .module('entrepApp.core')
    .directive('numSelect', ['$log', function ($log) {
      return {
        restrict: 'AE',
        transclude: false,
        scope: { nsWheels: "=wheels", nsAmount: "=amount" },
        templateUrl: 'app/core/num-select.tmp.html',
        link: function (scope, element, attrs) {
          var rotateMatrix = angular.isDefined(attrs.rotateMatrix) ? scope.$eval(attrs.rotateMatrix) : null;
          var wheels = scope.nsWheels;
          var values = [];
          var lastOffsets = [];
          var startX = 0, startY = 0;

          var amountLeft = Math.abs(scope.nsAmount);
          for (var index = 0; index < wheels.length; ++index) {
            lastOffsets.push([0, 0]);
            var av = amountLeft % 10;
            var wheel = wheels[wheels.length - index - 1];
            if (wheel.data.includes('-')) {
              if (scope.nsAmount < 0) {
                values.push(1)
              } else {
                values.push(0)
              }
            } else {
              var found = false;
              for (var k = 0; k < wheel.data.length; ++k) {
                if (wheel.data[k] == av) {
                  values.push(k);
                  found = true;
                  break;
                }
              }
              if (!found) {
                values.push(0);
              }
            }
            amountLeft = parseInt(amountLeft / 10);
          }
          values.reverse();

          element.ready(function() {
            onNumSelectInit();
          });

          function updateAmount() {
            var amountValue = 0;
            if(wheels[0].data.includes('-')){
              for (var index = 1; index < values.length; ++index) {
                var wheel = wheels[index];
                amountValue = amountValue * 10 + wheel.data[values[index]];
              }
              if(values[0]==1){
                scope.nsAmount = -amountValue;
              } else {
                scope.nsAmount = amountValue;
              }
            }else{
              for (var index = 0; index < values.length; ++index) {
                var wheel = wheels[index];
                amountValue = amountValue * 10 + wheel.data[values[index]];
              }
              scope.nsAmount = amountValue;
            }
            //scope.$apply();
          }

          scope.onHMPanStart = function(index, event) {
            // Prevent default dragging of selected content
            event.preventDefault();

            startX = event.center.x;
            startY = event.center.y;
            lastOffsets[index] = [0, 0];
          }

          scope.onHMPanMove = function(index, event) {
            // Prevent default dragging of selected content
            event.preventDefault();

            if (!lastOffsets[index]) {
              return;
            }

            var x = event.center.x - startX;
            var y = event.center.y - startY;
            var tx = x*2;
            var ty = y*2;
            if (rotateMatrix) {
              x = tx * rotateMatrix[0] + ty * rotateMatrix[1];
              y = tx * rotateMatrix[2] + ty * rotateMatrix[3]; 
            }

            var top = (-values[index] + 1) * 32 + y;
            if (top > 32 || top < (32 - (wheels[index].data.length - 1) * 32)) {
              return;
            }

            var container = angular.element(element.children()[0]);
            var elemContainer = angular.element(container.children()[0]);
            var elem = angular.element(elemContainer.children()[index]);
            elem.css({
              top:  top + 'px'
            });

            lastOffsets[index] = [x, y];
          }

          scope.onHMPanEnd = function(index, event) {
            // Prevent default dragging of selected content
            event.preventDefault();

            if (!lastOffsets[index]) {
              return;
            }

            values[index] -= Math.round(lastOffsets[index][1] / 32);
            var container = angular.element(element.children()[0]);
            var elemContainer = angular.element(container.children()[0]);
            var elem = angular.element(elemContainer.children()[index]);
            elem.css({
              top:  ((-values[index] + 1) * 32) + 'px'
            });
            updateAmount();

            lastOffsets[index] = null;
          }

          function onNumSelectInit () {
            var container = angular.element(element.children()[0]);
            var elemContainer = angular.element(container.children()[0]);
             elemContainer.css({
              width: wheels.length * 45 + 'px'
            });

            for (var index = 0; index < wheels.length; ++index) {
              var childElem = angular.element(elemContainer.children()[index]);
              childElem.css({
                left: index * 45 + 'px',
                top: (-values[index] + 1) * 32 + 'px'
              });
            }
          }
        }
      }
    }]);