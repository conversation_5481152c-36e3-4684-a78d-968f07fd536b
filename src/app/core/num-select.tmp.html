
<div class="numSelectContainer" layout="row" layout-align="center center">
  <div class="numSelectElemContainer">
    <div class="numSelectItem" ng-repeat="wheel in nsWheels" hm-panstart="onHMPanStart($index,$event)" hm-panmove="onHMPanMove($index,$event)" hm-panend="onHMPanEnd($index,$event)" >
      <div class="numSelectValueItem" ng-repeat="num in wheel.data">
        {{num}}
      </div>
    </div>  
  </div>
</div>