'use strict';

angular
    .module('entrepApp.core')
    .service('UserService', ['appCache', '$http', '$timeout', '$localStorage', '$window', 'appConfig', 
      'DiskCacheService', 'USER_ACTIONS','$rootScope', UserService]);

// Define the user sevice
function UserService(appCache, $http, $timeout, $localStorage, $window, appConfig, DiskCacheService, USER_ACTIONS, $rootScope) {
  var self = this;

  // var lastSendTime;
  var lastSendAction;
  var lastSendTime = {};
  
  self.getUser = getUser;
  self.register = register;
  self.requestAction = requestAction;
  self.updateCalculatedParams = _updateCalculatedParams;
  self.checkPermission = checkPermission;
  self.getOrderStatus = getOrderStatus;
  self.selectOrder = selectOrder;
  self.updateReport = updateReport;

  // setup the user actions
  for (var index = 0; index < USER_ACTIONS.length; ++index) {
    appCache.actions.push({ 
      id: USER_ACTIONS[index].id,
      name: USER_ACTIONS[index].name 
    });  
  }

  // gets user
  function getUser(success, error) {
    var reqUser = {
      method: 'GET',
      url: appConfig.serverUriBase + '/user/' + appCache.userID
    };
    var reqSystem = {
      method: 'GET',
      url: appConfig.serverUriBase + '/system'
    };

    $http(reqSystem).then(function(res) {
      angular.extend(appCache.system, res.data.system);
      return $http(reqUser);
    }).then(function(res) {
      console.log(JSON.stringify(res.data));
      angular.extend(appCache.user, res.data.user);
      _updateUserActions();
      _updateCalculatedParams();
      success();
    }).catch(function(e) {
      error(e);
    });
  }

  // register the user
  function register(body, success, error) {
    var req = {
      method: 'POST',
      url: appConfig.serverUriBase + '/user/register',
      data: body
    }
    $http(req).then(function(res) {
      appCache.user.status = 'Operation';
      if (DiskCacheService.isSupported()) {
        DiskCacheService.saveAccount({ 
          username: body.username,
          userID: appCache.userID,
          token: appCache.token
        });
      } else {
        $localStorage.user.username = body.username;
      }
      appCache.username = body.username;
      success();
    }).catch(function(e) {
      error(e);
    });
  }

  // request the action
  function requestAction(action, success, error) {
    if(lastSendTime[action.action]){
      var timeGap = (new Date().getTime()) - lastSendTime[action.action].getTime();
        if (timeGap < 5000) {
          console.log(timeGap);
          $rootScope.$broadcast('warn:notice', {title:'提醒',content:'点击过快，请稍后再试'});
          return;
        }
    }
    lastSendTime[action.action] = new Date();

    var req = {
      method: 'PUT',
      url: appConfig.serverUriBase + '/user/action',
      data: action,
      timeout: 5000
    }
    $http(req).then(function(res) {
      console.log(JSON.stringify(res.data));
      angular.extend(appCache.user, res.data.user);
      _updateUserActions();
      _updateCalculatedParams();
      if (appCache.user.status == 'Bankrupt') {
        $rootScope.$broadcast('warn:notice', {title:'提醒',content:'您已破产,5秒后将自动退出'});
        setTimeout(() => {
          if (DiskCacheService.isSupported()) {
            DiskCacheService.removeAccount();
          } else {
            delete $localStorage.user.token;  
          }
            
          delete appCache.token;
          $window.location.reload();
        }, 5000); 
      } else {
        success(res.data.order_breach_cash);
      }
      
    }).catch(function(e) {
      error(e);
    });
  }

  // get order status
  function getOrderStatus(success, error) {
    var req = {
      method: 'GET',
      url: appConfig.serverUriBase + '/system/order'
    }
    $http(req).then(function(res) {
      success(res.data);
    }).catch(function(e) {
      error(e);
    });
  }

  // setlect the order
  function selectOrder(orderID, success, error) {
    var req = {
      method: 'PUT',
      url: appConfig.serverUriBase + '/system/order',
      data: { userID: appCache.user.userID, orderID }
    }
    $http(req).then(function(res) {
      if (res.data.user) {
        angular.extend(appCache.user, res.data.user);
        //_updateUserActions();
        _updateCalculatedParams();
      }
      success(res.data);
    }).catch(function(e) {
      error(e);
    });
  }

  function checkPermission(action) {
    return appCache.user.permissions.indexOf(action) >= 0;
  }

  function updateReport(report, success, error){
    var req = {
      method: 'PUT',
      url: appConfig.serverUriBase + '/user/report',
      data: { userID: appCache.user.userID, report: report }
    }
    $http(req).then(function(res) {
      if (res.data.user) {
        angular.extend(appCache.user, res.data.user);
        //_updateUserActions();
        _updateCalculatedParams();
      }
      success(res.data);
    }).catch(function(e) {
      error(e);
    });
  }

  // update the user action
  function _updateUserActions() {
    for (var index = 0; index < appCache.actions.length; ++index) {
      var userAction = appCache.actions[index];
      userAction.enabled = (-1 != appCache.user.permissions.indexOf(userAction.id));
    }
    appCache.quanterStartEnabled = (-1 != appCache.user.permissions.indexOf('Quarter_Start'));
    appCache.quanterEndEnabled = (-1 != appCache.user.permissions.indexOf('Quarter_End'));
  }

  // update calculated params
  function _updateCalculatedParams() {
    appCache.calculatedParams.showCash = appCache.user.cash;
    // calculate bank loans
    var index = 0;
    var longLoans = appCache.calculatedParams.longLoans;
    var shortLoans = appCache.calculatedParams.shortLoans;
    var moveLongLoans = appCache.moveState.longLoans;
    var moveShortLoans = appCache.moveState.shortLoans;
    var Long_Loan_Max = appCache.system.settings.Long_Loan_Max;
    for (index = 0; index < Long_Loan_Max; ++index) {
      longLoans[index] = 0;
      moveLongLoans[index] = moveLongLoans[index] || false;
    }
    for (index = 0; index < 4; ++index) {
      shortLoans[index] = 0;
      moveShortLoans[index] = moveShortLoans[index] || false;
    }
    var totalLoan = 0;
    for (index = 0; index < appCache.user.bankLoans.length; ++index) {
      var bankLoan = appCache.user.bankLoans[index];
      if (bankLoan.BL_Remain_Date <= 0) continue;
      if (bankLoan.BL_Type == 'Short_Loan') {
        shortLoans[4 - bankLoan.BL_Remain_Date] += bankLoan.BL_Fee;
      } else if (bankLoan.BL_Type == 'Long_Loan') {
        longLoans[Long_Loan_Max - bankLoan.BL_Remain_Date] += bankLoan.BL_Fee;
      }
      totalLoan += bankLoan.BL_Fee;
    }
    appCache.moveState.longLoanMoveCompleted = true;
    for (index = Long_Loan_Max - 1; index >= 0; index--) {
      if (moveLongLoans[index]) {
        if (index == Long_Loan_Max - 1) {
          appCache.calculatedParams.showCash -= longLoans[index];
        } else {
          longLoans[index + 1] = longLoans[index];
        }
        longLoans[index] = 0;
      } else if (longLoans[index] > 0) {
        appCache.moveState.longLoanMoveCompleted = false;
      }
    }
    appCache.moveState.shortLoanMoveCompleted = true;
    for (index = 3; index >= 0; index--) {
      if (moveShortLoans[index]) {
        if (index == 3) {
          appCache.calculatedParams.showCash -= shortLoans[index];
        } else {
          shortLoans[index + 1] = shortLoans[index];
        }
        shortLoans[index] = 0;
      } else if (shortLoans[index] > 0) {
        appCache.moveState.shortLoanMoveCompleted = false;
      }
    }
    var equity = appCache.system.settings.Cash;
    if (appCache.user.hisCharges.length > 0) {
      var yearCharges = appCache.user.yearCharges;
      var lastYearCharges = appCache.user.hisCharges[appCache.user.hisCharges.length - 1];
      equity = lastYearCharges.VY_Total_Equity;
    }
      
    appCache.calculatedParams.availableLoan = appCache.system.settings.Loan_Ceiling * 
      equity - totalLoan;

    // update the receivables
    var receivables = appCache.calculatedParams.receivables;
    var moveReceivables = appCache.moveState.receivables;
    for (index = 0; index < 4; ++index) {
      receivables[index] = 0;
    }
    for (index = 0; index < appCache.user.receivables.length; ++index) {
      var item = appCache.user.receivables[index];
      var pos = item.R_Remain_Date - 1;
      receivables[pos] += item.R_Fee;
    }
    appCache.moveState.receivableMoveCompleted = true;
    for (index = 0; index < 4; ++index) {
      if (moveReceivables[index]) {
        if (index == 0) {
          appCache.calculatedParams.showCash += receivables[index];
        } else {
          receivables[index - 1] = receivables[index];
        }
        receivables[index] = 0;
      } else if (receivables[index] > 0) {
        appCache.moveState.receivableMoveCompleted = false;
      }
    }

    // update orders
    var productOrders = appCache.calculatedParams.productOrders;
    for (index = 0; index < appCache.system.codes.products.length; ++index) {
      var product = appCache.system.codes.products[index];
      productOrders[product.CP_ID - 1] = [];
    }
    for (index = 0; index < appCache.user.orderProducts.length; ++index) {
      var orderProduct = appCache.user.orderProducts[index];
      let orderCode = null;
      for (let orderIndex = 0; orderIndex < appCache.system.codes.orders.length; ++orderIndex) {
        var order = appCache.system.codes.orders[orderIndex];
        if (order.CO_ID == orderProduct.OP_COID) {
          orderCode = order;
          break;
        }
      }
      var productIndex = orderCode.CO_CPID - 1;
      productOrders[productIndex].push({
        order: orderProduct,
        code: orderCode 
      });
    }

    // update product developes
    var productDevelops = appCache.calculatedParams.productDevelops;
    for (index = 0; index < appCache.system.codes.products.length; ++index) {
      var product = appCache.system.codes.products[index];
      var productDevelop = productDevelops[product.CP_ID - 1] = { 
        productName: product.CP_Name, 
        developDate: product.CP_Develop_Date,
        progress: 0,
        items: []
      };
      for (var k = 0; k < productDevelop.developDate; ++k) {
        productDevelop.items[k] = false;
      }
    }
    for (index = 0; index < appCache.user.developProducts.length; ++index) {
      var developProduct = appCache.user.developProducts[index];
      var productDevelop = productDevelops[developProduct.DP_CPID - 1];
      productDevelop.progress = productDevelop.developDate - developProduct.DP_Remain_Date;
      productDevelop.DP_Finish_Date = developProduct.DP_Finish_Date;
      productDevelop.DP_Last_Date = developProduct.DP_Last_Date;
      for (var k = 0; k < productDevelop.developDate; ++k) {
        productDevelop.items[k] = productDevelop.progress > k ? true : false;
      }
    }

    // update market develops
    var marketDevelops = appCache.calculatedParams.marketDevelops;
    for (index = 0; index < appCache.system.codes.markets.length; ++index) {
      var market = appCache.system.codes.markets[index];
      var marketDevelop = marketDevelops[market.CM_ID - 1] = { 
        marketName: market.CM_Name, 
        developDate: market.CM_Develop_Date,
        developFee: market.CM_Develop_Fee,
        progress: 0,
        items: []
      };
      for (var k = 0; k < marketDevelop.developDate; ++k) {
        marketDevelop.items[k] = false;
      }
    }
    for (index = 0; index < appCache.user.developMarkets.length; ++index) {
      var developMarket = appCache.user.developMarkets[index];
      var marketDevelop = marketDevelops[developMarket.DM_CMID - 1];
      marketDevelop.progress = marketDevelop.developDate - developMarket.DM_Remain_Date;
      marketDevelop.DM_Finish_Date = developMarket.DM_Finish_Date;
      marketDevelop.DM_Last_Date = developMarket.DM_Last_Date;
      for (var k = 0; k < marketDevelop.developDate; ++k) {
        marketDevelop.items[k] = marketDevelop.progress > k ? true : false;
      }
    }

    // update iso develops
    var isoDevelops = appCache.calculatedParams.isoDevelops;
    for (index = 0; index < appCache.system.codes.isos.length; ++index) {
      var iso = appCache.system.codes.isos[index];
      var isoDevelop = isoDevelops[iso.CI_ID - 1] = { 
        isoName: iso.CI_Name, 
        developDate: iso.CI_Develop_Date,
        developFee: iso.CI_Develop_Fee,
        progress: 0,
        items: []
      };
      for (var k = 0; k < isoDevelop.developDate; ++k) {
        isoDevelop.items[k] = false;
      }
    }
    for (index = 0; index < appCache.user.developISOs.length; ++index) {
      var developISO = appCache.user.developISOs[index];
      var isoDevelop = isoDevelops[developISO.DI_CIID - 1];
      isoDevelop.progress = isoDevelop.developDate - developISO.DI_Remain_Date;
      isoDevelop.DI_Finish_Date = developISO.DI_Finish_Date;
      isoDevelop.DI_Last_Date = developISO.DI_Last_Date;
      for (var k = 0; k < isoDevelop.developDate; ++k) {
        isoDevelop.items[k] = isoDevelop.progress > k ? true : false;
      }
    }

    // init material orders & materials inventory
    var materialOrders = appCache.calculatedParams.materialOrders;
    for (index = 0; index < appCache.system.codes.materials.length; ++index) {
      var material = appCache.system.codes.materials[index];
      var materialOrder = materialOrders[material.CM_ID - 1] = { material, items: [] };
      for (var k = 0; k < material.CM_Lead_Date; ++k) {
        materialOrder.items[k] = 0;
      }
    }
    var materialsInventory = appCache.calculatedParams.materialsInventory;
    for (index = 0; index < appCache.system.codes.materials.length; ++index) {
      var material = appCache.system.codes.materials[index];
      materialsInventory[material.CM_ID - 1] = { material, num: 0 };
    }

    // update material orders
    for (index = 0; index < appCache.user.orderMaterials.length; ++index) {
      var orderMaterial = appCache.user.orderMaterials[index];
      var materialOrder = materialOrders[orderMaterial.OM_CMID - 1];
      materialOrder.items[orderMaterial.OM_Remain_Date - 1] += orderMaterial.OM_Num;
    }

    // update materials inventory
    for (index = 0; index < appCache.user.inventoryMaterials.length; ++index) {
      var inventoryMaterial = appCache.user.inventoryMaterials[index];
      materialsInventory[inventoryMaterial.IM_CMID - 1].num += inventoryMaterial.IM_Num;
    }

    appCache.moveState.materialOrderMoveCompleted = true;
    var moveMaterialOrders = appCache.moveState.materialOrders;
    for (index = 0; index < materialOrders.length; ++index) {
      var materialOrder = materialOrders[index];
      var moveMaterialOrder = moveMaterialOrders[index];
      if (materialOrder) {
        for (var k = 0; k < materialOrder.items.length; ++k) {
          if (moveMaterialOrder && moveMaterialOrder[k]) {
            if (k == 0) {
              materialsInventory[index].num += materialOrder.items[k];
            } else {
              materialOrder.items[k - 1] = materialOrder.items[k];
            }
            materialOrder.items[k] = 0;
          } else if (materialOrder.items[k] > 0) {
            appCache.moveState.materialOrderMoveCompleted = false;
          }
        }
      }
    }

    // update products inventory
    var productsInventory = appCache.calculatedParams.productsInventory;
    for (index = 0; index < appCache.system.codes.products.length; ++index) {
      var product = appCache.system.codes.products[index];
      productsInventory[product.CP_ID - 1] = { product, num: 0 };
    }
    for (index = 0; index < appCache.user.inventoryProducts.length; ++index) {
      var inventoryProduct = appCache.user.inventoryProducts[index];
      productsInventory[inventoryProduct.IP_CPID - 1].num = inventoryProduct.IP_Num;
    }

    // update the factory & product lines
    var workshops = appCache.calculatedParams.workshops;
    for (index = 0; index < appCache.system.codes.workshops.length; ++index) {
      var code = appCache.system.codes.workshops[index];
      var workshop = workshops[code.CW_ID - 1] = { code, productLines: [], mark: '空' };
      for (var k = 0; k < code.CW_Capacity; ++k) {
        workshop.productLines.push({ status: '', equity: 0, items: [] });
      }
    }
    for (index = 0; index < appCache.user.workshops.length; ++index) {
      var ws = appCache.user.workshops[index];
      var workshop = workshops[ws.W_CWID - 1];
      workshop.ws = ws;
      workshop.mark = ws.W_Status == 'Rent' ? '租' : (workshop.code.CW_Buy_Fee + 'M'); 
    }
    appCache.moveState.productMoveCompleted = true;
    var moveWorkshops = appCache.moveState.workshops;
    var moveProducts = appCache.moveState.products;
    for (index = 0; index < appCache.user.productLines.length; ++index) {
      var pl = appCache.user.productLines[index];
      var workshop = workshops[pl.PL_WID - 1];
      var productLine = workshop.productLines[pl.PL_ID - 1];
      productLine.pl = pl;
      productLine.code = appCache.system.codes.productLines[pl.PL_CPLID - 1];
      productLine.product = appCache.system.codes.products[pl.PL_CPID - 1];
      productLine.productBOMs = [];
      for (var k = 0; k < appCache.system.codes.productBOMs.length; ++k) {
        var productBOM = appCache.system.codes.productBOMs[k];
        if (productBOM.CPB_BOM_CPID == pl.PL_CPID) {
          productLine.productBOMs.push(productBOM);
        }
      }
      if (!pl.PL_Finish_Date) {
        productLine.equity = pl.PL_Invest;
      } else {
        productLine.equity = pl.PL_Invest - pl.PL_Dep_Total;
      }
      if (!pl.PL_Finish_Date) {
        for (var k = 0; k < productLine.code.CPL_Install_Date; ++k) {
          productLine.items[k] = { 
            status: 0, 
            value: pl.PL_Remain_Date > k ? '' : (productLine.code.CPL_Buy_Fee + 'M')
          };
        }
      } else if (!pl.PL_Transfer_Add_Date) {
        if (pl.PL_Product_Add_Date) {
          var moved = moveProducts[pl.PL_WID - 1] && moveProducts[pl.PL_WID - 1][pl.PL_ID - 1];
          var producingDate = moved ? pl.PL_Producing_Date - 1 : pl.PL_Producing_Date;
          for (var k = 0; k < productLine.code.CPL_Produce_Date; ++k) {
            productLine.items[k] = { 
              status: 3, 
              value: (producingDate == k ? productLine.product.CP_Name : '')
            };
          }
          if (producingDate < 0) {
            productsInventory[pl.PL_CPID - 1].num += 1;
          }
          if (!moved) {
            appCache.moveState.productMoveCompleted = false;
          }
        } else {
          for (var k = 0; k < productLine.code.CPL_Produce_Date; ++k) {
            productLine.items[k] = { status: 2, value: '' };
          }
          if (moveWorkshops[pl.PL_WID - 1] && moveWorkshops[pl.PL_WID - 1][pl.PL_ID - 1]) {
            var moveProductLine = moveWorkshops[pl.PL_WID - 1][pl.PL_ID - 1];
            var prepareItem = productLine.items[productLine.code.CPL_Produce_Date - 1];
            for (var k = 0; k < moveProductLine.length; ++k) {
              if (moveProductLine[k]) {
                var material = appCache.system.codes.materials[k];
                materialsInventory[k].num -= moveProductLine[k];
                var str = moveProductLine[k] > 1 ? moveProductLine[k] + material.CM_Name : material.CM_Name;
                if (prepareItem.value.length == 0) {
                  prepareItem.value = str;
                } else {
                  prepareItem.value += '+' + str;
                }
              }
            }
          }
        }
        
      } else {
        for (var k = 0; k < productLine.code.CPL_Transfer_Date; ++k) {
          productLine.items[k] = {
            status: 1, 
            value: pl.PL_Remain_Date > k ? '' : (productLine.code.CPL_Transfer_Fee + 'M')
          };
        }
      }
    }
  }

  
}
