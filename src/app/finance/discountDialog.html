<hm-dir init-size="[400,305]" init-rotate="0" rotate-matrix="[-1,0,0,-1]"  ng-cloak>

  <form layout-fill layout="column">
    <div class="inputDlgHead">
      <h4>贴现</h4>
    </div>

    <div flex layout-padding>

      <table class="table table-striped table-hover table-bordered">
        <thead>
          <tr>
            <th>剩余账期</th>
            <th>贴现额</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="recvValue in ll.appCache.calculatedParams.receivables track by $index">
            <td>{{['一','二','三','四'][$index]}}期</td>
            <td ng-click="ll.showInput=(recvValue>0);ll.currentItem=$index;" ng-class="{activeItem:(recvValue>0)}" >{{ll.request.params.nums[$index]}}</td>
          </tr>
        </tbody>
      </table>

    </div>

    <div layout="row" layout-align="center" class="inputActions">
      <md-button class="md-raised md-primary" ng-click="ll.confirm()" md-autofocus>
        确认
      </md-button>
      <md-button ng-click="ll.cancel()">
        取消
      </md-button>
    </div>

    <div ng-if="ll.showInput" class="inputContainer">
      <div data-no-move="true" class="selectContainer">
        <input ng-model="ll.currentValue" class="inputSelect" />
        <num-select wheels="ll.wheels" amount="ll.currentValue" rotate-matrix="[-1,0,0,-1]"></num-select>
        <div layout="row" layout-align="center center">
           <md-button class="md-raised md-primary" ng-disabled="ll.currentValue>ll.appCache.calculatedParams.receivables[ll.currentItem]" ng-click="ll.showInput=false;ll.request.params.nums[ll.currentItem]=ll.currentValue;" md-autofocus>
            确认
          </md-button>
        </div>
      </div>
    </div>
  </form>

</hm-dir>