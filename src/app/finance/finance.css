
.financeRegion {
  padding: 15px 5px 0px 5px;
}

.financeRegion .titleText {
  margin-right: 30px;
  margin-bottom: -5px;
}

.financeRegion .receivableArea .upArea {
  height: 150px;
  opacity: 0.6;
  background: #FBE5AA;
  border: 1px solid #EBBD3F;
  border-radius: 12px;
  margin-right: 10px;
  margin-left: 10px;
}

.financeRegion .receivableArea .mark {
  background: transparent;
}

.financeRegion .receivableArea .discountButton {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  line-height: 25px;
  text-align: center;
  background: #CA6403;
  font-size: 65%;
  color: white;
}

.discountMenu {
  transform:rotate(180deg);
}

.financeRegion .receivableArea .mark .title {
  padding: 5px;
  font-family: DFFangYuanW7;
  font-size: 15px;
  color: #B88350;
}

.financeRegion .receivableArea .itemArea {
  width: 250px;
  height: 100%;
}

.financeRegion .receivableArea .itemArea .item {
  background: #FFE07B;
  border: 1px solid #E59D11;
  border-radius: 4px;
  text-align: center;
  width: 60px;
  font-size: 90%;
}

.financeRegion .receivableArea .itemArea .item .title {
  padding-bottom: 20px;
  font-size: 12px;
  color: #CA6403;
}

.financeRegion .receivableArea .itemArea .item .value {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  line-height: 30px;
  margin-bottom: 10px;
  font-size: 90%;
  font-weight: bold;
  opacity: 0.9;
  background: #CA6403;
  color: white;
}

.financeRegion .cashArea {
  width: 150px;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 20px;
  margin-left: 10px;
  font-weight: bold;
  background: #FFE07B;
  border: 1px solid #E59D11;
  border-radius: 100px;
}

.financeRegion .cashArea .title {
  font-family: DFFangYuanW7;
  font-size: 18px;
  color: #CA6403;
  margin-bottom: 20px;
}

.financeRegion .cashArea .value {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  line-height: 60px;
  margin-bottom: 15px;
  margin-top: 25px;
  font-size: 120%;
  background: #FCAC12;
  color: white;
}

.financeRegion .loanArea {
  width: 320px;
}

.financeRegion .loanArea .longLoanArea {
  padding: 5px 0px 5px 5px;
  height: 130px;
  background: #FDE3A3;
  border: 1px solid #EBBD3F;
  border-radius: 8px;
}

.financeRegion .loanArea .longLoanArea .item {
  width: 54px;
  margin-right: 5px;
  height: 100%;
  text-align: center;
  background: #FFE07B;
  border: 1px solid #E59D11;
  border-radius: 4px;
}

.financeRegion .loanArea .item .title {
  padding-bottom: 10px;
  width: 100%;
  font-size: 12px;
  color: #CA6403;
}

.financeRegion .loanArea .item .value {
  opacity: 0.7;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  line-height: 32px;
  margin-top: 10px;
  margin-bottom: 5px;
  font-size: 80%;
  font-weight: bold;
  background: #CA6403;
  color: white;
}

.financeRegion .loanArea .shortLoanArea {
  padding: 5px;
  margin-top: 10px;
  height: 130px;
  background: #FDE3A3;
  border: 1px solid #EBBD3F;
  border-radius: 8px;
}

.financeRegion .loanArea .shortLoanArea .item {
  width: 65px;
  text-align: center;
  background: #FFE07B;
  border: 1px solid #E59D11;
  border-radius: 4px;
}

.financeRegion .longMark {
  font-family: DFFangYuanW7;
  font-size: 15px;
  color: #B88350;
  margin-bottom: 5px;
}

.financeRegion .shortMark {
  font-family: DFFangYuanW7;
  font-size: 15px;
  color: #B88350;
  margin-bottom: 5px;
}

.financeRegion .reportArea {
  margin-top: 10px;
  width: 360px;
  height: 260px;
}

.financeRegion .reportArea .item {
  width: 65px;
  height: 65px;
  font-size: 80%;
  border-radius: 50%;
  text-align: center;
  background: #FCAC12;
  border: 1px solid #E59D11;
  color: white;
}

.financeRegion .reportArea .item .value {
  margin-bottom: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #CA6403;
  line-height: 20px;
  font-size: 80%;
  font-weight: bold;
  opacity: 0.7;
}

.financeContainerCover .loanDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.financeContainerCover .loanDlgHead small {
  margin-left: 40px;
  color: white;
}

.financeContainerCover form {
  background-color: white;
}

.financeContainerCover .loanSelect {
  text-align: center;
  background: grey;
  color: white;
}

.financeContainerCover .loanInputContent {
  padding: 10px;
}

.financeContainerCover .loanInputActions {
  padding: 10px;
}

.financeContainerCover .inputDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.financeContainerCover .inputDlgHead small {
  margin-left: 40px;
}

.financeContainerCover form {
  background-color: white;
}

.financeContainerCover table th {
  text-align: center;
}

.financeContainerCover table td {
  text-align: center;
}

.financeContainerCover .activeItem {
  color: green;
}

.financeContainerCover .inputContainer {
  left: 0px;
  top: 0px;
  position: absolute;
  width: 100%;
  height: 100%;
  background: grey;
}

.financeContainerCover .inputSelect {
  width: 100%;
  text-align: center;
  background: yellow;
  color: black;
}

.financeContainerCover .selectContainer {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}