<div layout-fill layout="row" layout-align="space-between" class="financeRegion" ng-controller="FinanceController as fl">

    <div layout="row" layout-wrap layout-align="space-around" class="reportArea">
      <div class="item" layout="column" layout-align="center center" ng-repeat="item in fl.reportItems" ng-drop="true" ng-drop-success="fl.onDropOnReportItem($data,$index,$event)" >
        <div class="value">{{fl.appCache.user.yearCharges[item.id]||0}}M</div>
        <div>{{item.name}}</div>
      </div>
    </div>
    <div class="loanArea">
      <div layout="column">
        <div class="longLoanArea" layout="column">
          <div class="longMark">长期贷款</div>
          <div layout="row" flex layout-align="space-between" >
            <div ng-repeat="loanValue in fl.appCache.calculatedParams.longLoans track by $index" ng-drop="true" ng-drop-success="fl.onDropOnLongLoan($data,$index,$event)" layout="column" layout-align="end center" class="item">
              <div class="value" ng-drag="true" ng-drag-data="{source: 'LongLoan', index: $index}" allow-transform="true" ng-center-anchor="true" rotate-matrix="[-1,0,0,-1]">{{loanValue}}M</div>
              <div class="title" ng-click="fl.onLongLoan(5-$index,$event)">FY{{5-$index}}</div>
            </div>
          </div>
        </div>
        
        <div class="shortLoanArea" layout="column">
          <div class="shortMark">短期贷款</div>
          <div layout="row" flex layout-align="space-between">
            <div ng-repeat="loanValue in fl.appCache.calculatedParams.shortLoans track by $index" ng-drop="true" ng-drop-success="fl.onDropOnShortLoan($data,$index,$event)" layout="column" layout-align="end center" class="item">
              <div class="value" ng-drag="true" ng-drag-data="{source: 'ShortLoan', index: $index}" allow-transform="true" ng-center-anchor="true" rotate-matrix="[-1,0,0,-1]">{{loanValue}}M</div>
              <div class="title" ng-click="$index==0?fl.onShortLoan($event):''">Q{{4-$index}}</div>
            </div>
          </div>
        </div>
      </div>
      
      
    </div>
    <div layout="column" layout-align="end center" ng-drop="true" ng-drop-success="fl.onDropOnCash($data,$event)" class="cashArea">
      <img src="assets/finance/cash.png"/>
      <div class="value" ng-drag="true" ng-drag-data="{source: 'Cash'}" allow-transform="true" ng-center-anchor="true" rotate-matrix="[-1,0,0,-1]">{{fl.appCache.calculatedParams.showCash}}M</div>
      <div class="title">现金</div>
    </div>
    <div class="receivableArea" layout="column">
      <div flex>
        <div layout="column" class="upArea">
          <div layout="row" class="mark">
            <div class="title">应收款</div>
            <div flex></div>
            <div class="discountButton" ng-click="fl.onDiscountClicked($event)">贴现</div>
          </div>
          <div layout="row" flex layout-align="space-between" class="itemArea">
            <div ng-repeat="recvValue in fl.appCache.calculatedParams.receivables track by $index" ng-drop="true" ng-drop-success="fl.onDropReceivable($data,$index,$event)" layout="column" layout-align="end center" class="item">
              <div class="value" ng-drag="true" ng-drag-data="{source: 'Receivable', index: $index}" allow-transform="true" ng-center-anchor="true" rotate-matrix="[-1,0,0,-1]">{{recvValue}}M</div>
              <div class="title">{{['一','二','三','四'][$index]}}期</div>
            </div>
          </div>
        </div>
      </div>
      
      <div layout="row" layout-align="end">
        <img class="titleText" src="assets/finance/title.png">
      </div>
      
    </div>
  
</div>