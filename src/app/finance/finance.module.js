'use strict';

angular
    .module('entrepApp.finance', ['ngMaterial', 'ngDraggable', 'hmTouchEvents', 'entrepApp.core'])
    .controller('FinanceController', [
      'appCache', 'UserService', '$mdPanel', '$rootScope', '$log', FinanceController
    ])
    .controller('LongLoanDialogController', [
      'appCache', 'UserService', '$log', LongLoanDialogController
    ])
    .controller('ShortLoanDialogController', [
      'appCache', 'UserService', '$log', ShortLoanDialogController
    ])
    .controller('DiscountDialogController', [
      'appCache', 'UserService', '$log', DiscountDialogController
    ])
    .controller('FinanceConfirmController', [
      'appCache', 'UserService', '$log', FinanceConfirmController
    ]);

function FinanceController(appCache, UserService, $mdPanel, $rootScope, $log) {
  var self = this;

  self.reportItems = [
    { id:'VY_Tax', name:'税金' }, { id:'VY_Discount', name:'贴息' },
    { id:'VY_Interests', name: "利息"}, { id:'VY_Dep', name:'折旧费' },
    { id:'VY_Maintenance', name: '维修费'}, { id:'VY_Transfer', name:'转产费'},
    { id:'VY_Rent', name:'租金'}, { id:'VY_Overhaul', name:'管理费'},
    { id:'VY_AD', name:'广告费'}, { id:'VY_Damage', name:'其它'}
  ];
  self.appCache = appCache;
  self.longLoans = appCache.calculatedParams.longLoans;
  self.shortLoans = appCache.calculatedParams.shortLoans;
  self.moveLongLoans = appCache.moveState.longLoans;
  self.moveShortLoans = appCache.moveState.shortLoans;
  self.receivables = appCache.calculatedParams.receivables;
  self.moveReceivables = appCache.moveState.receivables;

  self.onLongLoan = onLongLoan;
  self.onShortLoan = onShortLoan;
  self.onDropOnLongLoan = onDropOnLongLoan;
  self.onDropOnShortLoan = onDropOnShortLoan;
  self.onDropOnCash = onDropOnCash;
  self.onDropReceivable = onDropReceivable;
  self.onDropOnReportItem = onDropOnReportItem;
  self.onDiscountClicked = onDiscountClicked;

  function onLongLoan(years, ev) {
    if (!UserService.checkPermission('Long_Loan')) {
      return;
    }

    var dlg = {
      controller: 'LongLoanDialogController as ll',
      templateUrl: 'app/finance/longLoanDialog.html',
      attachTo: angular.element(document.querySelector('.financeContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        request: {
          userID: appCache.userID,
          action: 'Long_Loan',
          params: {
            BL_Remain_Date: years,
            BL_Add_Date: appCache.user.date,
            BL_Type: 'Long_Loan'
          }
        }
      }
    };

    appCache.showFinanceCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }
  function onShortLoan(ev) {
    if (!UserService.checkPermission('Short_Loan')) {
      return;
    }

    var dlg = {
      controller: 'ShortLoanDialogController as ll',
      templateUrl: 'app/finance/shortLoanDialog.html',
      attachTo: angular.element(document.querySelector('.financeContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        request: {
          userID: appCache.userID,
          action: 'Short_Loan',
          params: {
            BL_Remain_Date: 4,
            BL_Add_Date: appCache.user.date,
            BL_Type: 'Short_Loan'
          }
        }
      }
    };

    appCache.showFinanceCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function onDropOnLongLoan(data, index, ev) {
    if (!UserService.checkPermission('Update_Long_Loan')) {
      return;
    }
    if (data.source != 'LongLoan') {
      return;
    }
    if (self.longLoans[data.index] == 0) {
      return;
    }
    if (data.index + 1 != index) {
      return;
    }
    if (self.longLoans[index] > 0) {
      return;
    }
    self.moveLongLoans[data.index] = true;
    UserService.updateCalculatedParams();
    _checkLongLoanMove();
  }

  function onDropOnShortLoan(data, index, ev) {
    if (!UserService.checkPermission('Update_Short_Loan')) {
      return;
    }
    if (data.source != 'ShortLoan') {
      return;
    }
    if (self.shortLoans[data.index] == 0) {
      return;
    }
    if (data.index + 1 != index) {
      return;
    }
    if (self.shortLoans[index] > 0) {
      return;
    }
    self.moveShortLoans[data.index] = true;
    UserService.updateCalculatedParams();
    _checkShortLoanMove();
  }

  function onDropOnCash(data, ev) {
    if (data.source == 'LongLoan') {
      if (!UserService.checkPermission('Update_Long_Loan')) {
        return;
      }
      var Long_Loan_Max = appCache.system.settings.Long_Loan_Max;
      if (data.index != Long_Loan_Max - 1) {
        return;
      }
      if (self.longLoans[data.index] == 0) {
        return;
      }
      self.moveLongLoans[data.index] = true;
      UserService.updateCalculatedParams();
      _checkLongLoanMove();
    } else if (data.source == 'ShortLoan') {
      if (!UserService.checkPermission('Update_Short_Loan')) {
        return;
      }
      if (data.index != 3) {
        return;
      }
      if (self.shortLoans[data.index] == 0) {
        return;
      }
      self.moveShortLoans[data.index] = true;
      UserService.updateCalculatedParams();
      _checkShortLoanMove();
    } else if (data.source == 'Receivable') {
      if (!UserService.checkPermission('Update_Receivable')) {
        return;
      }
      if (data.index != 0) {
        return;
      }
      /*if (self.receivables[data.index] == 0) {
        return;
      }*/
      self.moveReceivables[data.index] = true;
      UserService.updateCalculatedParams();
      _checkReceivableMove();
    }
  }

  function onDropReceivable(data, index, ev) {
    if (!UserService.checkPermission('Update_Receivable')) {
      return;
    }
    if (data.source != 'Receivable') {
      return;
    }
    if (self.receivables[data.index] == 0) {
      return;
    }
    if (data.index - 1 != index) {
      return;
    }
    if (self.receivables[index] > 0) {
      return;
    }
    self.moveReceivables[data.index] = true;
    UserService.updateCalculatedParams();
    _checkReceivableMove();
  }

  function onDropOnReportItem(data, index, ev) {
    if (index == 3) {
      if (!UserService.checkPermission('Pay_Dep')) {
        return;
      }
      if (data.source != 'LineValue') {
        return;
      }
      if (!data.productLine.pl) {
        return;
      }
      var pl = data.productLine.pl;
      if (!pl.PL_Finish_Date || pl.PL_Pay_Dep_Date == appCache.user.date) {
        return;
      }
      var year = Math.floor(pl.PL_Finish_Date / 10);
      var userYear = Math.floor(appCache.user.date / 10);
      var productLineCode = appCache.system.codes.productLines[pl.PL_CPLID - 1];
      if (userYear == year || userYear >= year + productLineCode.CPL_Dep_Date) {
        return;
      }

      var title = '申请支付设备折旧费';
      var content = '支付设备折旧费: ' + productLineCode.CPL_Dep_Fee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Pay_Dep',
          params: { PL_ID: pl.PL_ID, PL_WID: pl.PL_WID  }
        }
      });
    } else if (index == 0) {
      if (!UserService.checkPermission('Pay_Tax')) {
        return;
      }
      if (data.source != 'Cash') {
        return;
      }

      var title = '申请支付所得税';
      //var content = '支付所得税: ' + appCache.user.yearCharges.VY_Tax + 'M';
      console.log('---------支付所得税:---------');
      console.log(appCache.user.yearCharges.VY_Tax);
      var content = '支付所得税: ' + appCache.user.yearCharges.VY_Tax + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Pay_Tax',
          params: {}
        }
      });
    } else if (index == 2) {
      if (!UserService.checkPermission('Pay_Long_Loan_Interest')) {
        return;
      }
      if (data.source != 'Cash') {
        return;
      }

      var interests = 0;
      for (var index = 0; index < appCache.user.bankLoans.length; ++index) {
        var bankLoan = appCache.user.bankLoans[index];
        if (bankLoan.BL_Type == 'Long_Loan' && bankLoan.BL_Remain_Date > 0) {
          interests += Math.floor(bankLoan.BL_Fee * appCache.system.settings.Long_Loan_Interests / 100);
        }
      }

      var title = '申请支付长贷利息';
      var content = '支付长贷利息: ' + interests + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Pay_Long_Loan_Interest',
          params: {}
        }
      });
    }
  }

  function onDiscountClicked(ev) {
    var dlg = {
      controller: 'DiscountDialogController as ll',
      templateUrl: 'app/finance/discountDialog.html',
      attachTo: angular.element(document.querySelector('.financeContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        request: {
          userID: appCache.userID,
          action: 'Discount',
          params: {
            nums: [0 ,0, 0, 0]
          }
        }
      }
    };

    appCache.showFinanceCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function _checkLongLoanMove() {
    if (!appCache.moveState.longLoanMoveCompleted) {
      return;
    }
    self.moveLongLoans.splice(0, self.moveLongLoans.length);
    UserService.requestAction({
      userID: appCache.userID,
      action: 'Update_Long_Loan',
      params: {}
    }, function() {
      $log.info('Request completed.');
    }, function(e) {
      $log.error(e.data);
    });
  }
  function _checkShortLoanMove() {
    if (!appCache.moveState.shortLoanMoveCompleted) {
      return;
    }
    self.moveShortLoans.splice(0, self.moveShortLoans.length);
    UserService.requestAction({
      userID: appCache.userID,
      action: 'Update_Short_Loan',
      params: {}
    }, function() {
      $log.info('Request completed.');
    }, function(e) {
      $log.error(e.data);
    });
  }
  function _checkReceivableMove() {
    if (!appCache.moveState.receivableMoveCompleted) {
      return;
    }
    self.moveReceivables.splice(0, self.moveReceivables.length);
    UserService.requestAction({
      userID: appCache.userID,
      action: 'Update_Receivable',
      params: {}
    }, function() {
      $log.info('Request completed.');
    }, function(e) {
      $log.error(e.data);
    });
  }

  //listen to events of payment request, to run the dialog
  $rootScope.$on('event:paymentRequired', function(event, data) {
    if (appCache.showFinanceCover) {
      return;
    }
    
    // show the confirm dialog
    var dlg = {
      controller: 'FinanceConfirmController as fl',
      templateUrl: 'app/finance/financeConfirm.tmpl.html',
      attachTo: angular.element(document.querySelector('.financeContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        title: data.title,
        content: data.content,
        request: data.request
      }
    };

    appCache.showFinanceCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  });
}

function LongLoanDialogController(appCache, UserService, $log) {
  var self = this;

  self.appCache = appCache;

  self.cancel = cancel
  self.confirm = confirm;

  self.amount = 0;

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0]
    }
  ];

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showFinanceCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      self.request.params.BL_Fee = self.amount;
      UserService.requestAction(self.request, function() {
        $log.info('Request completed.');
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showFinanceCover = false;
    });
  };
}

function ShortLoanDialogController(appCache, UserService, $log) {
  var self = this;

  self.appCache = appCache;
  self.amount = 0;

  self.cancel = cancel
  self.confirm = confirm;

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 2, 4, 6, 8]
    }, 
    {
      circular: false,
      data: [0]
    }
  ];

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showFinanceCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      self.request.params.BL_Fee = self.amount;
      UserService.requestAction(self.request, function() {
        $log.info('Request completed.');
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showFinanceCover = false;
    });
  };
}

function DiscountDialogController(appCache, UserService, $log) {
  var self = this;

  self.appCache = appCache;
  self.currentValue = 0;
  self.currentItem = 0;

  self.cancel = cancel
  self.confirm = confirm;

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  ];

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showFinanceCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      var total = self.request.params.nums[0] + self.request.params.nums[1] + self.request.params.nums[2] + self.request.params.nums[3];
      if (total > 0) {
        UserService.requestAction(self.request, function() {
          $log.info('Request completed.');
        }, function(e) {
          $log.error(e.data);
        });
      }
      
      appCache.showFinanceCover = false;
    });
  };
}

function FinanceConfirmController(appCache, UserService, $log) {
  var self = this;

  self.confirm = confirm;
  self.cancel = cancel;
  self.disabled = false;
  self.order_breach_cash = 0;

  function cancel() {
    self.mdPanelRef.close().then(function() {
      UserService.updateCalculatedParams();
      appCache.showFinanceCover = false;
    });
  };

  function confirm() {
    UserService.requestAction(self.request, function(order_breach_cash) {
      if (order_breach_cash == 0) {
        self.mdPanelRef.close().then(function() {
          appCache.showFinanceCover = false;  
        });
      } else {
        self.disabled = true;
        self.order_breach_cash = order_breach_cash;
        self.content = '订单违约金 ' + order_breach_cash + ' M';
      }
    }, function(e) {
      self.disabled = true;
      self.content = e.data.error;
      $log.error(e.data);
    });
    
  };
}
