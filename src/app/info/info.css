

.infoRegion .left {
  width: 160px;
  padding-top: 20px;
  padding-left: 10px;
}

.infoRegion .left .upArea {
  font-family: DFFangYuanW7;
  font-size: 12px;
  color: #FFFFFF;

  padding: 15px 10px 15px 10px;
  background: #FFAB10;
  border: 2px solid #F8D594;
  box-shadow: inset 0 1px 3px 0 rgba(90,58,0,0.49);
  border-radius: 26px;
}

.infoRegion .left .button {
  font-family: DFFangYuanW7;
  font-size: 16px;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(102,56,0,0.61);

  margin: 18px 20px 0px 20px;
  border-radius: 23px;
  border: 2px solid white;
}

.infoRegion .left .quanterStart {
  background-image: linear-gradient(-180deg, #A4DF54 0%, #959595 99%);
}

.infoRegion .left .button:disabled {
  background-image: linear-gradient(-180deg, #E6E6E6 0%, grey 99%);
}

.infoRegion .left .quanterEnd {
  background-image: linear-gradient(-180deg, #F37738 0%, #FF3E0F 100%);
}

.infoRegion .left .logout {
  background-image: linear-gradient(-180deg, #E8BC27 0%, #FFAA0F 100%);
}

.infoRegion .titleText {
  position: absolute;
  bottom: -5px;
  left: 400px;
}

.infoRegion .right {
  padding-top: 20px;
}

.infoRegion .right .button {
  width: 40px;
  height: 70px;
  margin: 0px;
  padding: 0px;
}

.infoRegion .actionButton_1 {
  background: url(../../assets/info/1.png) no-repeat center;
}
.infoRegion .actionButton_1:disabled {
  background: url(../../assets/info/disabled/1.png) no-repeat center;
}
.infoRegion .actionButton_2 {
  background: url(../../assets/info/4.png) no-repeat center;
}
.infoRegion .actionButton_2:disabled {
  background: url(../../assets/info/disabled/4.png) no-repeat center;
}
.infoRegion .actionButton_3 {
  background: url(../../assets/info/5.png) no-repeat center;
}
.infoRegion .actionButton_3:disabled {
  background: url(../../assets/info/disabled/5.png) no-repeat center;
}
.infoRegion .actionButton_4 {
  background: url(../../assets/info/6.png) no-repeat center;
}
.infoRegion .actionButton_4:disabled {
  background: url(../../assets/info/disabled/6.png) no-repeat center;
}
.infoRegion .actionButton_5 {
  background: url(../../assets/info/2.png) no-repeat center;
}
.infoRegion .actionButton_5:disabled {
  background: url(../../assets/info/disabled/2.png) no-repeat center;
}
.infoRegion .actionButton_6 {
  background: url(../../assets/info/3.png) no-repeat center;
}
.infoRegion .actionButton_6:disabled {
  background: url(../../assets/info/disabled/3.png) no-repeat center;
}
.infoRegion .actionButton_7 {
  background: url(../../assets/info/7.png) no-repeat center;
}
.infoRegion .actionButton_7:disabled {
  background: url(../../assets/info/disabled/7.png) no-repeat center;
}
.infoRegion .actionButton_8 {
  background: url(../../assets/info/8.png) no-repeat center;
}
.infoRegion .actionButton_8:disabled {
  background: url(../../assets/info/disabled/8.png) no-repeat center;
}
.infoRegion .actionButton_9 {
  background: url(../../assets/info/10.png) no-repeat center;
}
.infoRegion .actionButton_9:disabled {
  background: url(../../assets/info/disabled/10.png) no-repeat center;
}
.infoRegion .actionButton_10 {
  background: url(../../assets/info/13.png) no-repeat center;
}
.infoRegion .actionButton_10:disabled {
  background: url(../../assets/info/disabled/13.png) no-repeat center;
}
.infoRegion .actionButton_11 {
  background: url(../../assets/info/9.png) no-repeat center;
}
.infoRegion .actionButton_11:disabled {
  background: url(../../assets/info/disabled/9.png) no-repeat center;
}
.infoRegion .actionButton_12 {
  background: url(../../assets/info/11.png) no-repeat center;
}
.infoRegion .actionButton_12:disabled {
  background: url(../../assets/info/disabled/11.png) no-repeat center;
}
.infoRegion .actionButton_13 {
  background: url(../../assets/info/12.png) no-repeat center;
}
.infoRegion .actionButton_13:disabled {
  background: url(../../assets/info/disabled/12.png) no-repeat center;
}
.infoRegion .actionButton_14 {
  background: url(../../assets/info/14.png) no-repeat center;
}
.infoRegion .actionButton_14:disabled {
  background: url(../../assets/info/disabled/14.png) no-repeat center;
}
.infoRegion .actionButton_15 {
  background: url(../../assets/info/15.png) no-repeat center;
}
.infoRegion .actionButton_15:disabled {
  background: url(../../assets/info/disabled/15.png) no-repeat center;
}
.infoRegion .actionButton_16 {
  background: url(../../assets/info/16.png) no-repeat center;
}
.infoRegion .actionButton_16:disabled {
  background: url(../../assets/info/disabled/16.png) no-repeat center;
}
.infoRegion .actionButton_17 {
  background: url(../../assets/info/17.png) no-repeat center;
}
.infoRegion .actionButton_17:disabled {
  background: url(../../assets/info/disabled/17.png) no-repeat center;
}
.infoRegion .actionButton_18 {
  background: url(../../assets/info/21.png) no-repeat center;
}
.infoRegion .actionButton_18:disabled {
  background: url(../../assets/info/disabled/21.png) no-repeat center;
}
.infoRegion .actionButton_19 {
  background: url(../../assets/info/22.png) no-repeat center;
}
.infoRegion .actionButton_19:disabled {
  background: url(../../assets/info/disabled/22.png) no-repeat center;
}
.infoRegion .actionButton_20 {
  background: url(../../assets/info/23.png) no-repeat center;
}
.infoRegion .actionButton_20:disabled {
  background: url(../../assets/info/disabled/23.png) no-repeat center;
}
.infoRegion .actionButton_21 {
  background: url(../../assets/info/24.png) no-repeat center;
}
.infoRegion .actionButton_21:disabled {
  background: url(../../assets/info/disabled/24.png) no-repeat center;
}
.infoRegion .actionButton_22 {
  background: url(../../assets/info/18.png) no-repeat center;
}
.infoRegion .actionButton_22:disabled {
  background: url(../../assets/info/disabled/18.png) no-repeat center;
}
.infoRegion .actionButton_23 {
  background: url(../../assets/info/20.png) no-repeat center;
}
.infoRegion .actionButton_23:disabled {
  background: url(../../assets/info/disabled/20.png) no-repeat center;
}
.infoRegion .actionButton_24 {
  background: url(../../assets/info/30.png) no-repeat center;
}
.infoRegion .actionButton_24:disabled {
  background: url(../../assets/info/disabled/30.png) no-repeat center;
}
.infoRegion .actionButton_25{
  background: url(../../assets/info/29.png) no-repeat center;
}
.infoRegion .actionButton_25:disabled {
  background: url(../../assets/info/disabled/29.png) no-repeat center;
}
.infoRegion .actionButton_26 {
  background: url(../../assets/info/28.png) no-repeat center;
}
.infoRegion .actionButton_26:disabled {
  background: url(../../assets/info/disabled/28.png) no-repeat center;
}
.infoRegion .actionButton_27 {
  background: url(../../assets/info/25.png) no-repeat center;
}
.infoRegion .actionButton_27:disabled {
  background: url(../../assets/info/disabled/25.png) no-repeat center;
}
.infoRegion .actionButton_28 {
  background: url(../../assets/info/26.png) no-repeat center;
}
.infoRegion .actionButton_28:disabled {
  background: url(../../assets/info/disabled/26.png) no-repeat center;
}
.infoRegion .actionButton_29 {
  background: url(../../assets/info/27.png) no-repeat center;
}
.infoRegion .actionButton_29:disabled {
  background: url(../../assets/info/disabled/27.png) no-repeat center;
}
.infoRegion .actionButton_30 {
  background: url(../../assets/info/19.png) no-repeat center;
}
.infoRegion .actionButton_30:disabled {
  background: url(../../assets/info/disabled/19.png) no-repeat center;
}

.infoRegion .icon-refresh {

  font-family: DFFangYuanW7;
  font-size: 16px;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(102, 56, 0, 0.61);

  margin: 18px 20px 0px 20px;
  border-radius: 23px;
  border: 2px solid white;

  position: absolute;
  bottom: 5px;
  left: 140px;
}

.infoContainerCover .inputDlgHead, .infoContainerGuideCover .inputDlgHead{
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.infoContainerCover .inputDlgHead small {
  margin-left: 40px;
}

.infoContainerCover form ,  .infoContainerGuideCover form{
  background-color: white;
}

.infoContainerGuideCover .guideIcon {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}
.infoContainerCover .flex-center, .infoContainerGuideCover .flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.infoContainerCover .inputContainer {
  left: 0px;
  top: 0px;
  position: absolute;
  width: 100%;
  height: 100%;
  background: grey;
}

.infoContainerCover .selectContainer {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}

.infoContainerCover .inputSelect {
  width: 100%;
  text-align: center;
  background: yellow;
  color: black;
}

.infoContainerCover .report-green {
  color: green;
}

.infoContainerCover .report-red {
  color: red;
}

.infoContainerCover .report-normal {
  color: unset;
}

.infoContainerCover md-tab-item {
  max-width: unset !important;
}

.infoContainerCover .report-form{
  height: auto;
  background-color: white;
}

.infoContainerCover .report-form input::-webkit-outer-spin-button,input::-webkit-inner-spin-button {
  -webkit-appearance: none;
 }
.infoContainerCover .report-form input[type="number"] {
  -moz-appearance: textfield;
}
