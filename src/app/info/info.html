<div layout-fill class="infoRegion" id="entrepInfoContainer" ng-controller="InfoController as il">
  
  <div flex layout="row">
    <div class="left">
      <div class="upArea">
        <div layout="row">
          <label flex>当前用户</label>
          <label flex>{{il.appCache.user.username}}</label>
        </div>
        <div layout="row">
          <label flex>当前时间</label>
          <label flex>{{'第'+((il.appCache.user.date-il.appCache.user.date%10)/10)+'年第'+il.appCache.user.date%10+'季'}}</label>
        </div>  
      </div>
      
      <div layout="column">
        <md-button ng-disabled="!il.appCache.quanterStartEnabled" ng-click="il.onQuarterStart($event)" class="button quanterStart">当季开始</md-button>
        <md-button ng-disabled="!il.appCache.quanterEndEnabled" ng-click="il.onQuarterEnd($event)" class="button quanterEnd">
          {{il.user.date%10==4?'当年结束':'当季结束'}}
        </md-button>
        <md-button class="button logout" ng-click="il.logout($event)">退出系统</md-button>
        <!-- <md-button class="button refresh" ng-click="il.refresh($event)">刷新数据</md-button> -->
        <md-button class="button fillReport" ng-click="il.fillReport($event)">填写报表</md-button>
      </div>
    </div>

    <div flex class="right" layout="row" layout-wrap>
      <md-button ng-repeat="userAction in il.appCache.actions" class="button" ng-class="'actionButton_'+($index+1)" ng-click="il.showGuide(userAction.id,$event)" ng-disabled="!userAction.enabled" aria-label="Action">
      </md-button>
      
    </div>
  </div>
  <md-button
    class="button refresh icon-refresh"
    ng-click="il.refresh($event)"
  >
    刷新
  </md-button>
  <!-- <md-icon md-svg-icon="refresh" class="icon-refresh" ng-click="il.refresh($event)"></md-icon> -->
  <img class="titleText" src="assets/info/title.png">
  </img>
</div>