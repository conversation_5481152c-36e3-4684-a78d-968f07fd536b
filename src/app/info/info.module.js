'use strict';

angular
    .module('entrepApp.info', ['ngMaterial', 'entrepApp.core'])
    .controller('InfoController', [
      'appCache', 'AuthService', 'UserService', '$mdDialog', '$mdPanel', '$rootScope', '$log', InfoController
    ])
    .controller('InfoConfirmController', [
      'appCache', 'AuthService', 'UserService', '$rootScope', '$log', InfoConfirmController
    ])
    .controller('RefreshConfirmController', [
      'appCache', 'AuthService', 'UserService', '$rootScope', '$log', RefreshConfirmController
    ])
    .controller('ReportController', [
      'appCache', 'AuthService', 'UserService', '$rootScope', '$log', ReportController
    ])
    ;

function InfoController(appCache, AuthService, UserService, $mdDialog, $mdPanel, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;
  self.user = appCache.user;
  self.logout = logout;
  self.refresh = refresh;
  self.onAction = onAction;
  self.onQuarterStart = onQuarterStart;
  self.onQuarterEnd = onQuarterEnd;
  self.showGuide = showGuide;
  self.fillReport = fillReport;
  
  // the logout function
  function logout(ev) {
    // show the confirm dialog
     var title = '退出系统';
    var content = '是否确认退出系统？';

    _doShowConfirmPanel(title, content);
  }

  // the refresh function
  function refresh(ev) {
    var title = '刷新数据';
    var content = '确认刷新数据吗？';
    _doShowRefreshConfirmPanel(title, content);
    //_doShowConfirmPanel(title, content);
  }

  function fillReport(ev){
    var dlg = {
      controller: 'ReportController as rl',
      templateUrl: 'app/info/reportDialog.html',
      attachTo: angular.element(document.querySelector('.infoContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        // request: data.request
      }
    };
    if(Object.keys(appCache.user.yearChargesReport).length!==0){
      appCache.showInfoCover = true;
      $mdPanel.open(dlg).then(function(mdPanelRef) {
        // nothing
      });
    }
  }

  // handle the action
  function onAction(actionId, ev) {
    // 关闭动画
    self.mdPanelRef.close().then(function() {
      appCache.showInfoGuideCover = false;
    });
    
    switch (actionId) {
    case 'Year_Meeting':
      onYearMetting(ev);
      break;

    case 'Pay_Rent':
      onPayRent(ev);
      break;

    case 'Pay_Maintenance':
      onPayMaintenance(ev);
      break;

    case 'Attend_Order':
      onAttendOrder(ev);
      break;
      
    default:
      break;
    }
  }

  // 新手指引
  function showGuide(actionId,ev) {
    // show the confirm dialog
   
    var action = appCache.actions.find(item => item.id == actionId)
    var title =  action.name;
    var content = actionId
    var url = `assets/info/guide/${actionId}.png`
  
    var dlg = {
      controller: 'InfoController as il',
      templateUrl: 'app/info/infoGuide.tmpl.html',
      attachTo: angular.element(document.querySelector('.infoContainerGuideCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        title, content,url
      }
    };
    appCache.showInfoGuideCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      
    });
  }

  


  // handle year meeting
  function onYearMetting(ev) {
    // show the confirm dialog
    var title = '新年规划';
    var content = '召开新年规划会议？';

    _doShowConfirmPanel(title, content,
      {
        userID: appCache.userID,
        action: 'Year_Meeting',
        params: {}
      }
    );
  }

  // handle quarter start
  function onQuarterStart(ev) {
    // show the confirm dialog
    var title = '当季开始';
    var content = '当季盘点？';

    _doShowConfirmPanel(title, content,
      {
        userID: appCache.userID,
        action: 'Quarter_Start',
        params: {}
      }
    );
  }

  function onQuarterEnd(ev) {
    // show the confirm dialog
    var title = self.user.date % 10 == 4 ? '当季结束' : '当年结束';
    var content = '缴纳管理费？';

    _doShowConfirmPanel(title, content, null, 
      {
        title: '申请缴纳管理费',
        content: '申请缴纳管理费: ' + appCache.system.settings.Overhaul + 'M',
        request: {
          userID: appCache.userID,
          action: 'Quarter_End',
          params: {}
        }
      }
    );
  }

  function onPayRent(ev) {
    // show the confirm dialog
    var title = '支付厂房租金';
    var content = '支付厂房租金？';

    var rent = 0;
    for (let index = 0; index < self.user.workshops.length; ++index) {
      const workshop = self.user.workshops[index];
      if (workshop.W_Status == 'Rent' && workshop.W_Pay_Date < self.user.date && 
        workshop.W_Pay_Date % 10 == self.user.date % 10) {
        const wsCode = appCache.system.codes.workshops[workshop.W_CWID - 1];
        rent += wsCode.CW_Rent_Fee;
      }
    }
    _doShowConfirmPanel(title, content, null,
      {
        title: '申请支付厂房租金',
        content: '申请支付厂房租金:' + rent + 'M',
        request: {
          userID: appCache.userID,
          action: 'Pay_Rent',
          params: {}
        }
      }
    );
  }

  function onPayMaintenance(ev) {
    // show the confirm dialog
    var title = '支付设备维护费';
    var content = '支付设备维护费？';
    
    var maintenanceFee = 0;
    for (let index = 0; index < self.user.productLines.length; ++index) {
      const productLine = self.user.productLines[index];
      if (productLine.PL_Finish_Date) {
        const productLineCode = appCache.system.codes.productLines[productLine.PL_CPLID - 1];
        maintenanceFee += productLineCode.CPL_Maintenance_Fee;
      }  
    }

    _doShowConfirmPanel(title, content, null,
      {
        title: '申请支付设备维护费',
        content: '申请支付设备维护费:' + maintenanceFee + 'M',
        request: {
          userID: appCache.userID,
          action: 'Pay_Maintenance',
          params: {}
        }
      }
    );
  }

  function onAttendOrder(ev) {
    $rootScope.$broadcast('event:attendOrder', {});
  }

  function _doShowConfirmPanel(title, content, request, broadcast) {
    // show the confirm dialog
    var dlg = {
      controller: 'InfoConfirmController as il',
      templateUrl: 'app/info/infoConfirm.tmpl.html',
      attachTo: angular.element(document.querySelector('.infoContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        title, content, request, broadcast
      }
    };

    appCache.showInfoCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function _doShowRefreshConfirmPanel(title, content, request, broadcast) {
    // show the confirm dialog
    var dlg = {
      controller: 'RefreshConfirmController as il',
      templateUrl: 'app/info/infoConfirm.tmpl.html',
      attachTo: angular.element(document.querySelector('.infoContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        title, content, request, broadcast
      }
    };

    appCache.showInfoCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }
}

function InfoConfirmController(appCache, AuthService, UserService, $rootScope, $log) {
  var self = this;

  self.confirm = confirm;
  self.cancel = cancel;

  function cancel() {
    self.mdPanelRef.close().then(function() {
      UserService.updateCalculatedParams();
      appCache.showInfoCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      
      if (self.request) {
        UserService.requestAction(self.request, function() {
          // nothing
        }, function(e) {
          $log.error(e.data);
        });  
      } else if (self.broadcast) {
        $rootScope.$broadcast('event:paymentRequired', self.broadcast);
      } else {
        AuthService.logout();
      }
      
      appCache.showInfoCover = false;
    });
  };
}

function RefreshConfirmController(appCache, AuthService, UserService, $rootScope, $log) {
  var self = this;

  self.confirm = confirm;
  self.cancel = cancel;

  function cancel() {
    self.mdPanelRef.close().then(function() {
      UserService.updateCalculatedParams();
      appCache.showInfoCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      
      UserService.getUser(function() {}, function(err) {});
      
      appCache.showInfoCover = false;
    });
  };
}

function ReportController(appCache, AuthService, UserService, $rootScope, $log) {
  var self = this;
  self.appCache = appCache;
  self.yearChargesReport = appCache.user.yearChargesReport;
  self.submit = submit;
  self.onclick = onclick;
  self.save = save;
  self.cancel = cancel;
  self.checkData = checkData;
  self.checkTotal = checkTotal;
  self.onWheelSubmit = onWheelSubmit;
  

  self.showInput = false;
  self.currentItem = null;
  self.currentValue = 0;

  self.wheels = [
    {
      circular: true,
      data: ['+','-'] 
    },
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  ];

  var len = self.appCache.user.hisCharges.length;
  self.calculatedReport = self.appCache.user.hisCharges[len-1];

  function submit() {
    self.yearChargesReport.Submitted = true;
    UserService.updateReport(self.yearChargesReport, function() {
      $log.info('Request completed.');
    }, function(e) {
      $log.error(e.data);
    });

  };

  function save() {
    self.mdPanelRef.close().then(function() {
      UserService.updateReport(self.yearChargesReport, function() {
        $log.info('Request completed.');
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showInfoCover = false;
    });
  };

  function cancel(){
    self.mdPanelRef.close().then(function(){
      appCache.showInfoCover = false;
    });
  }

  function onclick(item){
    if(self.yearChargesReport.Submitted){
      return;
    }
    self.showInput = true;
    self.currentItem = item;
    self.currentValue = self.yearChargesReport[item];
  }

  function checkData(item) {
    if (self.yearChargesReport.Submitted) {
      if(self.calculatedReport[item] == undefined || self.calculatedReport[item] == null){
        self.calculatedReport[item] = 0;
      }
      switch (item) {
        case 'VY_Gross_Profit':
          if (self.yearChargesReport[item] === (self.calculatedReport.VY_Sales - self.calculatedReport.VY_Direct_Cost)) {
            return 'report-green';
          } else {
            return 'report-red';
          }
        case 'VY_Profit_Before_Dep':
          if (self.yearChargesReport[item] === (self.calculatedReport.VY_Sales - self.calculatedReport.VY_Direct_Cost - self.calculatedReport.VY_Total_Fee)) {
            return 'report-green';
          } else {
            return 'report-red';
          }
        case 'VY_Profit_Before_Interest':
          if (self.yearChargesReport[item] === (self.calculatedReport.VY_Sales - self.calculatedReport.VY_Direct_Cost - self.calculatedReport.VY_Total_Fee - self.calculatedReport.VY_Dep)) {
            return 'report-green';
          } else {
            return 'report-red';
          }
        case 'VY_Financial_Expenses':
          if (self.yearChargesReport[item] === (self.calculatedReport.VY_Interests + self.calculatedReport.VY_Discount)) {
            return 'report-green';
          } else {
            return 'report-red';
          }
        default:
          if (self.yearChargesReport[item] === self.calculatedReport[item]) {
            return 'report-green';
          } else {
            return 'report-red';
          }
      }
    }
  }

  // 资产总计 负债和所有者权益总计 是否相等
  function checkTotal(){
    if(self.yearChargesReport.VY_Total_Asset === self.yearChargesReport.VY_Total){
      return true;
    }else{
      return false;
    }
  }

  function _doCalculate(){
    // 合计
    self.yearChargesReport.VY_Total_Fee = self.yearChargesReport.VY_Overhaul + self.yearChargesReport.VY_AD +
      self.yearChargesReport.VY_Maintenance + self.yearChargesReport.VY_Damage +
      self.yearChargesReport.VY_Transfer + self.yearChargesReport.VY_Rent +
      self.yearChargesReport.VY_Develop_Market + self.yearChargesReport.VY_Develop_ISO +
      self.yearChargesReport.VY_Develop_Product + self.yearChargesReport.VY_Information;
    // 毛利
    self.yearChargesReport.VY_Gross_Profit = self.yearChargesReport.VY_Sales - self.yearChargesReport.VY_Direct_Cost;
    // 折旧前利润
    self.yearChargesReport.VY_Profit_Before_Dep = self.yearChargesReport.VY_Gross_Profit - self.yearChargesReport.VY_Total_Fee;
    // 支付利息前利润
    self.yearChargesReport.VY_Profit_Before_Interest = self.yearChargesReport.VY_Profit_Before_Dep - self.yearChargesReport.VY_Dep;
    // 税前利润
    self.yearChargesReport.VY_Profit_Before_Tax = self.yearChargesReport.VY_Profit_Before_Interest - self.yearChargesReport.VY_Financial_Expenses;
    // 年度净利润
    self.yearChargesReport.VY_Annual_Net_Profit = self.yearChargesReport.VY_Profit_Before_Tax - self.yearChargesReport.VY_Tax;
    // 流动资产合计
    self.yearChargesReport.VY_Total_Current_Asset = self.yearChargesReport.VY_Cash + self.yearChargesReport.VY_Receivable +
      self.yearChargesReport.VY_Product_In_Process + self.yearChargesReport.VY_Product +
      self.yearChargesReport.VY_Material;
    // 固定资产合计
    self.yearChargesReport.VY_Total_Fixed_Asset = self.yearChargesReport.VY_Workshop + self.yearChargesReport.VY_Equipment +
      self.yearChargesReport.VY_Project_On_Construction;
    // 资产总计
    self.yearChargesReport.VY_Total_Asset = self.yearChargesReport.VY_Total_Current_Asset + self.yearChargesReport.VY_Total_Fixed_Asset;
    // 负债合计
    self.yearChargesReport.VY_Total_Liability = self.yearChargesReport.VY_Long_Loan + self.yearChargesReport.VY_Short_Loan + 
      self.yearChargesReport.VY_Tax;
    // 所有者权益合计
    self.yearChargesReport.VY_Total_Equity = self.yearChargesReport.VY_Init_Equity + self.yearChargesReport.VY_Retained_Earning +
      self.yearChargesReport.VY_Annual_Net_Profit;
    // 负债和所有者权益总计
    self.yearChargesReport.VY_Total = self.yearChargesReport.VY_Total_Liability + self.yearChargesReport.VY_Total_Equity;
  }

  function onWheelSubmit(){
    self.showInput=false;
    self.yearChargesReport[self.currentItem]=parseInt(self.currentValue);
    _doCalculate();
  }
}