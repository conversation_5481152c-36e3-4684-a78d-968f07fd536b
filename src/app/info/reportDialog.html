<hm-dir
    rotate-matrix="[0,1,-1,0]"
    init-size="[600,'auto']"
    ng-cloak
>
    <form
        layout-fill
        layout="column"
        class="report-form"
    >
        <div class="inputDlgHead">
            <h4>填写报表</h4>
        </div>
        <md-tabs
            class="userTabs"
            md-border-bottom
            md-selected="0"
            md-stretch-tabs="always"
            md-dynamic-height
        >
            <md-tab label="综合费用表">
                <table
                    class="table table-hover table-bordered"
                    style="text-align: center"
                >
                    <thead>
                        <tr ng-if="!rl.yearChargesReport.Submitted">
                            <th style="width: 50%;text-align: center;">用户名</th>
                            <th style="text-align: center;">
                                {{rl.appCache.user.username}}
                            </th>
                        </tr>

                        <tr ng-if="rl.yearChargesReport.Submitted">
                            <th style="width: 33.3%;text-align: center;">项目</th>
                            <th style="width: 33.3%;text-align: center;">
                                已填写报表
                            </th>
                            <th style="text-align: center;">
                                正确报表
                            </th>
                        </tr>

                    </thead>
                    <tbody>
                        <tr>
                            <td>管理费</td>
                            <td
                                ng-click="rl.onclick('VY_Overhaul')"
                                ng-class="rl.checkData('VY_Overhaul')"
                            >
                                {{rl.yearChargesReport.VY_Overhaul}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Overhaul}}M
                            </td>
                        </tr>
                        <tr>
                            <td>广告费</td>
                            <td
                                ng-click="rl.onclick('VY_AD')"
                                ng-class="rl.checkData('VY_AD')"
                            >
                                {{rl.yearChargesReport.VY_AD}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_AD}}M
                            </td>
                        </tr>
                        <tr>
                            <td>维修费</td>
                            <td
                                ng-click="rl.onclick('VY_Maintenance')"
                                ng-class="rl.checkData('VY_Maintenance')"
                            >
                                {{rl.yearChargesReport.VY_Maintenance}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Maintenance}}M
                            </td>
                        </tr>
                        <tr>
                            <td>其他</td>
                            <td
                                ng-click="rl.onclick('VY_Damage')"
                                ng-class="rl.checkData('VY_Damage')"
                            >
                                {{rl.yearChargesReport.VY_Damage}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Damage}}M
                            </td>
                        </tr>
                        <tr>
                            <td>转产费</td>
                            <td
                                ng-click="rl.onclick('VY_Transfer')"
                                ng-class="rl.checkData('VY_Transfer')"
                            >
                                {{rl.yearChargesReport.VY_Transfer}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Transfer}}M
                            </td>
                        </tr>
                        <tr>
                            <td>厂房租金</td>
                            <td
                                ng-click="rl.onclick('VY_Rent')"
                                ng-class="rl.checkData('VY_Rent')"
                            >
                                {{rl.yearChargesReport.VY_Rent}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Rent}}M
                            </td>
                        </tr>
                        <tr>
                            <td>新市场开拓</td>
                            <td
                                ng-click="rl.onclick('VY_Develop_Market')"
                                ng-class="rl.checkData('VY_Develop_Market')"
                            >
                                {{rl.yearChargesReport.VY_Develop_Market}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Develop_Market}}M
                            </td>
                        </tr>
                        <tr>
                            <td>ISO资格认证</td>
                            <td
                                ng-click="rl.onclick('VY_Develop_ISO')"
                                ng-class="rl.checkData('VY_Develop_ISO')"
                            >
                                {{rl.yearChargesReport.VY_Develop_ISO}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Develop_ISO}}M
                            </td>
                        </tr>
                        <tr>
                            <td>产品研发</td>
                            <td
                                ng-click="rl.onclick('VY_Develop_Product')"
                                ng-class="rl.checkData('VY_Develop_Product')"
                            >
                                {{rl.yearChargesReport.VY_Develop_Product}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Develop_Product}}M
                            </td>
                        </tr>
                        <tr>
                            <td>信息费</td>
                            <td
                                ng-click="rl.onclick('VY_Information')"
                                ng-class="rl.checkData('VY_Information')"
                            >
                                {{rl.yearChargesReport.VY_Information}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Information}}M
                            </td>
                        </tr>
                        <tr>
                            <td>合计</td>
                            <td ng-class="rl.checkData('VY_Total_Fee')">
                                {{rl.yearChargesReport.VY_Overhaul + rl.yearChargesReport.VY_AD +
                                rl.yearChargesReport.VY_Maintenance + rl.yearChargesReport.VY_Damage +
                                rl.yearChargesReport.VY_Transfer + rl.yearChargesReport.VY_Rent +
                                rl.yearChargesReport.VY_Develop_Market + rl.yearChargesReport.VY_Develop_ISO +
                                rl.yearChargesReport.VY_Develop_Product + rl.yearChargesReport.VY_Information}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Fee}}M
                            </td>
                        </tr>
                    </tbody>
                </table>
            </md-tab>

            <md-tab label="利润表">
                <table
                    class="table table-striped table-hover table-bordered"
                    style="text-align: center"
                >
                    <thead>
                        <tr ng-if="!rl.yearChargesReport.Submitted">
                            <th style="width: 50%;text-align: center;">用户名</th>
                            <th style="text-align: center;">
                                {{rl.appCache.user.username}}
                            </th>
                        </tr>

                        <tr ng-if="rl.yearChargesReport.Submitted">
                            <th style="width: 33.3%;text-align: center;">项目</th>
                            <th style="width: 33.3%;text-align: center;">
                                已填写报表
                            </th>
                            <th style="text-align: center;">
                                正确报表
                            </th>
                        </tr>

                    </thead>
                    <tbody>
                        <tr>
                            <td>销售收入</td>
                            <td
                                ng-click="rl.onclick('VY_Sales')"
                                ng-class="rl.checkData('VY_Sales')"
                            >
                                {{rl.yearChargesReport.VY_Sales}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Sales}}M
                            </td>
                        </tr>
                        <tr>
                            <td>直接成本</td>
                            <td
                                ng-click="rl.onclick('VY_Direct_Cost')"
                                ng-class="rl.checkData('VY_Direct_Cost')"
                            >
                                {{rl.yearChargesReport.VY_Direct_Cost}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Direct_Cost}}M
                            </td>
                        </tr>
                        <tr>
                            <td>毛利</td>
                            <td ng-class="rl.checkData('VY_Gross_Profit')">
                                {{rl.yearChargesReport.VY_Sales-rl.yearChargesReport.VY_Direct_Cost}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Sales-rl.calculatedReport.VY_Direct_Cost}}M
                            </td>
                        </tr>
                        <tr>
                            <td>综合费用</td>
                            <td
                                ng-class="rl.checkData('VY_Total_Fee')"
                            >
                                {{rl.yearChargesReport.VY_Total_Fee}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Fee}}M
                            </td>
                        </tr>
                        <tr>
                            <td>折旧前利润</td>
                            <td ng-class="rl.checkData('VY_Profit_Before_Dep')">
                                {{rl.yearChargesReport.VY_Sales-rl.yearChargesReport.VY_Direct_Cost-
                                rl.yearChargesReport.VY_Total_Fee}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Sales-rl.calculatedReport.VY_Direct_Cost-
                                rl.calculatedReport.VY_Total_Fee}}M
                            </td>
                        </tr>
                        <tr>
                            <td>折旧</td>
                            <td
                                ng-click="rl.onclick('VY_Dep')"
                                ng-class="rl.checkData('VY_Dep')"
                            >
                                {{rl.yearChargesReport.VY_Dep}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Dep}}M
                            </td>
                        </tr>
                        <tr>
                            <td>支付利息前利润</td>
                            <td ng-class="rl.checkData('VY_Profit_Before_Interest')">
                                {{rl.yearChargesReport.VY_Sales-rl.yearChargesReport.VY_Direct_Cost-
                                rl.yearChargesReport.VY_Total_Fee-rl.yearChargesReport.VY_Dep}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Sales-rl.calculatedReport.VY_Direct_Cost-
                                rl.calculatedReport.VY_Total_Fee-rl.calculatedReport.VY_Dep}}M
                            </td>
                        </tr>
                        <tr>
                            <td>财务费用</td>
                            <td
                                ng-click="rl.onclick('VY_Financial_Expenses')"
                                ng-class="rl.checkData('VY_Financial_Expenses')"
                            >
                                {{rl.yearChargesReport.VY_Financial_Expenses}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Interests+rl.calculatedReport.VY_Discount}}M
                            </td>
                        </tr>
                        <tr>
                            <td>税前利润</td>
                            <td ng-class="rl.checkData('VY_Profit_Before_Tax')">
                                {{rl.yearChargesReport.VY_Sales-rl.yearChargesReport.VY_Direct_Cost-
                                rl.yearChargesReport.VY_Total_Fee-rl.yearChargesReport.VY_Dep-
                                rl.yearChargesReport.VY_Financial_Expenses}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Profit_Before_Tax}}M
                            </td>
                        </tr>
                        <tr>
                            <td>所得税</td>
                            <td
                                ng-click="rl.onclick('VY_Tax')"
                                ng-class="rl.checkData('VY_Tax')"
                            >
                                {{rl.yearChargesReport.VY_Tax}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Tax}}M
                            </td>
                        </tr>
                        <tr>
                            <td>年度净利润</td>
                            <td ng-class="rl.checkData('VY_Annual_Net_Profit')">
                                {{rl.yearChargesReport.VY_Sales-rl.yearChargesReport.VY_Direct_Cost-
                                rl.yearChargesReport.VY_Total_Fee-rl.yearChargesReport.VY_Dep-
                                rl.yearChargesReport.VY_Financial_Expenses-rl.yearChargesReport.VY_Tax}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Annual_Net_Profit}}M
                            </td>
                        </tr>
                    </tbody>
                </table>
            </md-tab>

            <md-tab label="资产负债表">
                <table
                    class="table table-striped table-hover table-bordered"
                    style="text-align: center"
                >
                    <thead>
                        <tr ng-if="!rl.yearChargesReport.Submitted">
                            <th style="width: 50%;text-align: center;">用户名</th>
                            <th style="text-align: center;">
                                {{rl.appCache.user.username}}
                            </th>
                        </tr>

                        <tr ng-if="rl.yearChargesReport.Submitted">
                            <th style="width: 33.3%;text-align: center;">项目</th>
                            <th style="width: 33.3%;text-align: center;">
                                已填写报表
                            </th>
                            <th style="text-align: center;">
                                正确报表
                            </th>
                        </tr>

                    </thead>
                    <tbody>
                        <tr>
                            <td>现金</td>
                            <td
                                ng-click="rl.onclick('VY_Cash')"
                                ng-class="rl.checkData('VY_Cash')"
                            >
                                {{rl.yearChargesReport.VY_Cash}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Cash}}M
                            </td>
                        </tr>
                        <tr>
                            <td>应收款</td>
                            <td
                                ng-click="rl.onclick('VY_Receivable')"
                                ng-class="rl.checkData('VY_Receivable')"
                            >
                                {{rl.yearChargesReport.VY_Receivable}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Receivable}}M
                            </td>
                        </tr>
                        <tr>
                            <td>在制品</td>
                            <td
                                ng-click="rl.onclick('VY_Product_In_Process')"
                                ng-class="rl.checkData('VY_Product_In_Process')"
                            >
                                {{rl.yearChargesReport.VY_Product_In_Process}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Product_In_Process}}M
                            </td>
                        </tr>
                        <tr>
                            <td>产成品</td>
                            <td
                                ng-click="rl.onclick('VY_Product')"
                                ng-class="rl.checkData('VY_Product')"
                            >
                                {{rl.yearChargesReport.VY_Product}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Product}}M
                            </td>
                        </tr>
                        <tr>
                            <td>原料</td>
                            <td
                                ng-click="rl.onclick('VY_Material')"
                                ng-class="rl.checkData('VY_Material')"
                            >
                                {{rl.yearChargesReport.VY_Material}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Material}}M
                            </td>
                        </tr>
                        <tr>
                            <td>流动资产合计</td>
                            <td ng-class="rl.checkData('VY_Total_Current_Asset')">
                                {{rl.yearChargesReport.VY_Cash + rl.yearChargesReport.VY_Receivable +
                                rl.yearChargesReport.VY_Product_In_Process + rl.yearChargesReport.VY_Product +
                                rl.yearChargesReport.VY_Material}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Current_Asset}}M
                            </td>
                        </tr>
                        <tr>
                            <td>厂房</td>
                            <td
                                ng-click="rl.onclick('VY_Workshop')"
                                ng-class="rl.checkData('VY_Workshop')"
                            >
                                {{rl.yearChargesReport.VY_Workshop}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Workshop}}M
                            </td>
                        </tr>
                        <tr>
                            <td>机器设备</td>
                            <td
                                ng-click="rl.onclick('VY_Equipment')"
                                ng-class="rl.checkData('VY_Equipment')"
                            >
                                {{rl.yearChargesReport.VY_Equipment}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Equipment}}M
                            </td>
                        </tr>
                        <tr>
                            <td>在建工程</td>
                            <td
                                ng-click="rl.onclick('VY_Project_On_Construction')"
                                ng-class="rl.checkData('VY_Project_On_Construction')"
                            >
                                {{rl.yearChargesReport.VY_Project_On_Construction}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Project_On_Construction}}M
                            </td>
                        </tr>
                        <tr>
                            <td>固定资产合计</td>
                            <td
                                ng-click="rl.onclick('VY_Total_Fixed_Asset')"
                                ng-class="rl.checkData('VY_Total_Fixed_Asset')"
                            >
                                {{rl.yearChargesReport.VY_Workshop + rl.yearChargesReport.VY_Equipment +
                                rl.yearChargesReport.VY_Project_On_Construction}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Fixed_Asset}}M
                            </td>
                        </tr>
                        <tr>
                            <td>资产总计</td>
                            <td
                                ng-class="rl.yearChargesReport.Submitted?rl.checkData('VY_Total_Asset'):rl.checkTotal()?'report-normal':'report-red'">
                                {{rl.yearChargesReport.VY_Cash + rl.yearChargesReport.VY_Receivable +
                                rl.yearChargesReport.VY_Product_In_Process + rl.yearChargesReport.VY_Product +
                                rl.yearChargesReport.VY_Material + rl.yearChargesReport.VY_Workshop +
                                rl.yearChargesReport.VY_Equipment + rl.yearChargesReport.VY_Project_On_Construction}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Asset}}M
                            </td>
                        </tr>
                        <tr>
                            <td>长期负债</td>
                            <td
                                ng-click="rl.onclick('VY_Long_Loan')"
                                ng-class="rl.checkData('VY_Long_Loan')"
                            >
                                {{rl.yearChargesReport.VY_Long_Loan}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Long_Loan}}M
                            </td>
                        </tr>
                        <tr>
                            <td>短期负债</td>
                            <td
                                ng-click="rl.onclick('VY_Short_Loan')"
                                ng-class="rl.checkData('VY_Short_Loan')"
                            >
                                {{rl.yearChargesReport.VY_Short_Loan}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Short_Loan}}M
                            </td>
                        </tr>
                        <tr>
                            <td>所得税</td>
                            <td
                                ng-click="rl.onclick('VY_Tax')"
                                ng-class="rl.checkData('VY_Tax')"
                            >
                                {{rl.yearChargesReport.VY_Tax}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Tax}}M
                            </td>
                        </tr>
                        <tr>
                            <td>负债合计</td>
                            <td ng-class="rl.checkData('VY_Total_Liability')">
                                {{rl.yearChargesReport.VY_Long_Loan + rl.yearChargesReport.VY_Short_Loan +
                                rl.yearChargesReport.VY_Tax}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Liability}}M
                            </td>
                        </tr>
                        <tr>
                            <td>股东资本</td>
                            <td
                                ng-click="rl.onclick('VY_Init_Equity')"
                                ng-class="rl.checkData('VY_Init_Equity')"
                            >
                                {{rl.yearChargesReport.VY_Init_Equity}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Init_Equity}}M
                            </td>
                        </tr>
                        <tr>
                            <td>利润留存</td>
                            <td
                                ng-click="rl.onclick('VY_Retained_Earning')"
                                ng-class="rl.checkData('VY_Retained_Earning')"
                            >
                                {{rl.yearChargesReport.VY_Retained_Earning}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Retained_Earning}}M
                            </td>
                        </tr>
                        <tr>
                            <td>年度净利</td>
                            <td
                                ng-class="rl.checkData('VY_Annual_Net_Profit')"
                            >
                                {{rl.yearChargesReport.VY_Annual_Net_Profit}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Annual_Net_Profit}}M
                            </td>
                        </tr>
                        <tr>
                            <td>所有者权益合计</td>
                            <td ng-class="rl.checkData('VY_Total_Equity')">
                                {{rl.yearChargesReport.VY_Init_Equity + rl.yearChargesReport.VY_Retained_Earning +
                                rl.yearChargesReport.VY_Annual_Net_Profit}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total_Equity}}M
                            </td>
                        </tr>
                        <tr>
                            <td>负债和所有者权益总计</td>
                            <td
                                ng-class="rl.yearChargesReport.Submitted?rl.checkData('VY_Total'):rl.checkTotal()?'report-normal':'report-red'">
                                {{rl.yearChargesReport.VY_Long_Loan + rl.yearChargesReport.VY_Short_Loan +
                                rl.yearChargesReport.VY_Tax + rl.yearChargesReport.VY_Init_Equity +
                                rl.yearChargesReport.VY_Retained_Earning + rl.yearChargesReport.VY_Annual_Net_Profit}}M
                            </td>
                            <td ng-if="rl.yearChargesReport.Submitted">
                                {{rl.calculatedReport.VY_Total}}M
                            </td>
                        </tr>
                    </tbody>
                </table>
            </md-tab>

        </md-tabs>
        <div
            layout="row"
            layout-align="center"
            class="inputActions"
        >
            <md-button
                class="md-raised md-primary"
                ng-click="rl.submit()"
                ng-if="!rl.yearChargesReport.Submitted"
                md-autofocus
                ng-disabled="!rl.appCache.user.permissions.includes('Fill_Report') || !rl.checkTotal()"
            >
                提交
            </md-button>
            <md-button
                ng-click="rl.save()"
                ng-if="!rl.yearChargesReport.Submitted"
            >
                保存
            </md-button>
            <md-button
                class="md-raised md-primary"
                ng-click="rl.cancel()"
                ng-if="rl.yearChargesReport.Submitted"
            >
                确认
            </md-button>
        </div>
        <div
            ng-if="rl.showInput"
            class="inputContainer"
        >
            <div
                data-no-move="true"
                class="selectContainer"
            >
                <input
                    ng-model="rl.currentValue"
                    type="number"
                    class="inputSelect"
                />
                <num-select
                    wheels="rl.wheels"
                    amount="rl.currentValue"
                    rotate-matrix="[0,1,-1,0]"
                ></num-select>
                <div
                    layout="row"
                    layout-align="center center"
                >
                    <md-button
                        class="md-raised md-primary"
                        ng-click="rl.onWheelSubmit()"
                        md-autofocus
                    >
                        确认
                    </md-button>
                </div>
            </div>
        </div>
    </form>
</hm-dir>