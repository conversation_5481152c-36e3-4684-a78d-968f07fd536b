<div ng-controller="LoginController as ll" layout-fill class="entrepLogin" ng-cloak>

  <div class="header">
    <h1 md-display-3>多点触控企业经营模拟沙盘</h1>
    <h2 md-display-3>学生端</h2>
  </div>

  <div ng-if="ll.appCache.loading" layout="row" layout-fill layout-align="center center">
    <md-progress-circular md-mode="indeterminate"></md-progress-circular>
  </div>

  <md-content ng-if="!ll.appCache.loading">
    <form class="md-padding" name="loginForm" layout="column" layout-align="center center">
      <div layout="row" class="addressRow">
        <md-input-container flex class="md-block">
          <!-- Use floating placeholder instead of label -->
          <md-icon md-svg-icon="server" class="name"></md-icon> 
          <input ng-model="ll.host" name="input" type="text" placeholder="服务器地址" ng-virtual-keyboard required>
        </md-input-container>
        <md-input-container flex class="md-block">
          <input ng-model="ll.port" name="input" type="text" placeholder="端口" ng-virtual-keyboard required>
        </md-input-container>
      </div>
      <md-input-container class="md-block">
        <!-- Use floating placeholder instead of label -->
        <md-icon md-svg-icon="account" class="name"></md-icon> 
        <input ng-model="ll.user.username" name="input" type="text" placeholder="用户名" ng-virtual-keyboard required>
      </md-input-container>
      <md-input-container class="md-block">
        <!-- Use floating placeholder instead of label -->
        <md-icon md-svg-icon="textbox-password" class="password"></md-icon> 
        <input ng-model="ll.user.password" name="input" type="password" placeholder="密码" ng-virtual-keyboard required>
      </md-input-container>
      <span class="error" ng-show="ll.loginFailed">{{ll.loginError}}</span>
      <md-button md-no-ink ng-disabled="!loginForm.$valid" ng-click="ll.login()" class="md-raised md-primary action" md-autofocus>登录</md-button>
    </form>
  </md-content>

</div>