'use strict';

angular
    .module('entrepApp.login', ['ngMaterial','angular-virtual-keyboard', 'entrepApp.core'])
    .controller('LoginController', [
      'appConfig', 'appCache', '$log', 'AuthService', LoginController
    ]);

function LoginController(appConfig, appCache, $log, AuthService) {
  var self = this;
  const storage = require("electron-json-storage");
  var data = (storage.getSync('config',{ dataPath: "cache" }) && storage.getSync('config',{ dataPath: "cache" }).serverUriBase) ? storage.getSync('config',{ dataPath: "cache" }) : appConfig;
  var uri = data.serverUriBase.substr(7);
  uri = uri.substr(0, uri.indexOf('/'));

  self.appCache = appCache;
  self.host = uri.substr(0, uri.indexOf(':'));
  self.port = uri.substr(uri.indexOf(':') + 1);

  self.user = {};
  self.login = login;

  // the login function
  function login() {
    self.loginFailed = false;
    appConfig.serverUriBase = 'http://' + self.host + ':' + self.port + '/api/v1';
    AuthService.login(self.user, function() {
      $log.info('logged!');
      console.log("--------login 2 appcache--------");
      console.log(appCache);
    }, function(e) {
      self.loginFailed = true;
      self.loginError = e.data;
    });
  }
}