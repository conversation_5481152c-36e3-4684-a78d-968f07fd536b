<hm-dir init-size="[400,316]" init-rotate="0" ng-cloak>

  <form layout-fill layout="column">
    <md-tabs md-border-bottom md-selected="0" md-center-tabs md-dynamic-height md-stretch-tabs="always">
      <md-tab label="紧急采购原料">
        <md-content layout-fill layout="row" >
          <table class="table table-striped table-hover table-bordered">
            <thead>
              <tr>
                <th>原料名称</th>
                <th>价格</th>
                <th>数量</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="inventory in ll.materialsInventory">
                <td>{{inventory.material.CM_Name}}</td>
                <td>{{inventory.material.CM_Buy_Fee*ll.appCache.system.settings.Emergence_Multiple_Material}}M</td>
                <td ng-click="ll.showInput=true;ll.currentItem=$index;ll.productActive=false;" >{{ll.request.params.material[$index]}}</td>
              </tr>
            
            </tbody>
          </table>
        </md-content>
      </md-tab>

      <md-tab label="紧急采购成品">
        <md-content layout-fill layout="row" >
          <table class="table table-striped table-hover table-bordered">
            <thead>
              <tr>
                <th>产品名称</th>
                <th>价格</th>
                <th>数量</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="inventory in ll.productsInventory">
                <td>{{inventory.product.CP_Name}}</td>
                <td>{{inventory.product.CP_Direct_Cost*ll.appCache.system.settings.Emergence_Multiple_Product}}M</td>
                <td ng-click="ll.showInput=true;ll.currentItem=$index;ll.productActive=true;" >{{ll.request.params.product[$index]}}</td>
              </tr>
            
            </tbody>
          </table>
        </md-content>
      </md-tab>
    </md-tabs>


    <div layout="row" layout-align="center" class="inputActions">
      <md-button class="md-raised md-primary" ng-click="ll.confirm()" md-autofocus>
        确认
      </md-button>
      <md-button ng-click="ll.cancel()">
        取消
      </md-button>
    </div>

    <div ng-if="ll.showInput" class="inputContainer">
      <div data-no-move="true" class="selectContainer">
        <input ng-model="ll.currentValue" class="actionInputSelect" />
        <num-select wheels="ll.wheels" amount="ll.currentValue"></num-select>
        <div layout="row" layout-align="center center">
           <md-button class="md-raised md-primary" ng-click="ll.showInput=false;ll.productActive?ll.request.params.product[ll.currentItem]=ll.currentValue:ll.request.params.material[ll.currentItem]=ll.currentValue;" md-autofocus>
            确认
          </md-button>
        </div>
      </div>
    </div>
  </form>

</hm-dir>