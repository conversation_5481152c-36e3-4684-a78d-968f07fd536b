
.logisticRegion .main {
  padding: 5px;
}

.logisticRegion .titleText {
  position: absolute;
  bottom: -5px;
}

.logisticRegion .actionColumn {
  position:absolute;
  bottom: 15px;
  right: 10px;
}

.logisticRegion .buyButtonWrapper {
  background: #FFFFFF;
  box-shadow: 0 1px 1px 0 rgba(0,0,0,0.16);
  border-radius: 47px;
  margin-bottom: 0px;
  margin-top: 10px;
  margin-right: 10px;
  padding: 0px;
}

.logisticRegion .buyButton {
  padding-left: 16px;
  padding-right: 16px;
  font-family: DFFangYuanW7;
  font-size: 18px;
  line-height: 18px;
  color: #FFFFFF;
  text-shadow: 0 1px 1px rgba(0,0,0,0.27);

  margin: 3px;
  background: #FF820A;
  border: 1px solid #FF6060;
  box-shadow: 0 -1px 4px 0 rgba(169,92,33,0.32), inset 0 2px 5px 1px rgba(245,92,46,0.75);
  border-radius: 47px;
}

.logisticRegion .material {
  background: #FCAC12;
  border: 1px solid #E59D11;
  border-radius: 12px;

  margin: 10px 3px 0px 10px;
  width: 125px;
  height: 80px;
  padding: 3px;
}

.logisticRegion .material .materialIcon {
  margin: 6px;
}

.logisticRegion .material .title {
  text-align: left;
  padding: 5px 0px 5px 0px;
  color: white;
  font-size: 90%;
}

.logisticRegion .material .number {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #FFFFFF;
  text-align: center;
  line-height: 35px;
  color: #FCAC12;
}

.logisticRegion .R1 {
  
}

.logisticRegion .R2 {

}

.logisticRegion .R3 {

}

.logisticRegion .R4 {

}

.logisticRegion .order {
  background: #D6E26E;
  border: 1px solid #95B763;
  border-radius: 12px;

  margin: 10px 3px 0px 10px;
  width: 125px;
  height: 80px;
  padding: 3px;
}

.logisticRegion .order .orderIcon {
  margin: 10px 10px 6px 6px;
}

.logisticRegion .order .title {
  text-align: left;
  padding: 5px 0px 5px 0px;
  color: white;
  font-size: 90%;
}

.logisticRegion .order .number {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #FFFFFF;
  text-align: center;
  line-height: 35px;
  color: #FCAC12;
}

.logisticRegion .truck {
  background: #BBDAC6;
  border: 1px solid #69CAC8;
  border-radius: 12px;

  margin: 10px 3px 0px 10px;
  width: 125px;
  height: 80px;
  padding: 3px;
}

.logisticRegion .truck .carIcon {
  margin: 6px;
}

.logisticRegion .truck .title {
  text-align: left;
  padding: 5px 0px 5px 0px;
  color: white;
  font-size: 90%;
}

.logisticRegion .truck .number {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #FFFFFF;
  text-align: center;
  line-height: 35px;
  color: #FCAC12;
}

.logisticRegion .production {
  width: 110px;
  height: 180px; 
}

.logisticRegion .production .title {
  text-align: center;
  padding: 5px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #FF820A;
}

.logisticRegion .production .number {
  margin-top: 10px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  text-align: center;
  line-height: 35px;
  opacity: 0.55;
  background: #CA6403;
  color: white;
}

.logisticRegion .P1 {

}

.logisticRegion .P2 {

}

.logisticRegion .P3 {

}

.logisticRegion .P4 {

}

.logisticContainerCover .inputDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.logisticContainerCover .inputDlgHead small {
  margin-left: 40px;
}

.logisticContainerCover form {
  background-color: white;
}

.logisticContainerCover .inputSelect {
  text-align: center;
  background: grey;
  color: white;
}

.logisticContainerCover .inputDlgContent {
  padding: 10px;
}

.logisticContainerCover .inputDlgActions {
  padding: 10px;
}

.logisticContainerCover table th {
  text-align: center;
}

.logisticContainerCover table td {
  text-align: center;
}

.logisticContainerCover .activeItem {
  color: green;
}

.logisticContainerCover .inputContainer {
  left: 0px;
  top: 0px;
  position: absolute;
  width: 100%;
  height: 100%;
  background: grey;
}

.logisticContainerCover .actionInputSelect {
  width: 100%;
  text-align: center;
  background: yellow;
  color: black;
}

.logisticContainerCover .selectContainer {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}

.logisticContainerCover .inputDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.logisticContainerCover .inputDlgHead small {
  margin-left: 40px;
}