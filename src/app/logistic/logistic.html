<div layout-fill class="logisticRegion" layout="column" ng-controller="LogisticController as ll">
  <div class="main" layout-align="space-between" layout="row">
    <div layout="column" class="left">
      <div layout="row" class="top">
        <div ng-repeat="inventory in ll.materialsInventory" layout="column" ng-drop="true" ng-drop-success="ll.onDropOnMaterialInventory($data,$index,$event)" class="material ng-class:inventory.material.CM_Name;">
          <div layout="row">
            <img class="materialIcon" src="assets/logistic/material.png"/>
            <div layout="column">
              <div class="title">
                {{inventory.material.CM_Name}}原料库
              </div>
              <div class="number" ng-drag="true" ng-drag-data="{source: 'MaterialInventory', index: $index}" data-allow-transform="true" ng-center-anchor="true">
                {{inventory.num}}
              </div>
            </div>
          </div>
          
        </div>
      </div>
      <div layout="row" class="bottom">
        <div ng-repeat="materialOrder in ll.materialOrders" layout="column">
          <div ng-if="materialOrder.items.length>1" layout="column" ng-drop="true" ng-drop-success="ll.onDropOnTruck($data,$index,$event)" class="truck">
            <div layout="row">
              <img class="carIcon" src="assets/logistic/car.png"/>
              <div layout="column">
                <div class="title">
                  {{materialOrder.material.CM_Name}}在途
                </div>
                <div class="number" ng-drag="true" ng-drag-data="{source: 'MaterialTruck', index: $index, item: 0}" data-allow-transform="true" ng-center-anchor="true" >
                  {{materialOrder.items[0]}}
                </div>
              </div>
            </div>
            
          </div>
          <div layout="column" class="order">
            <div layout="row">
              <img class="orderIcon" src="assets/logistic/order.png"/>
              <div layout="column">
                <div class="title" ng-click="ll.onMaterialOrderClicked($index,$event)">
                  {{materialOrder.material.CM_Name}}订单
                </div>
                <div class="number" ng-drag="true" ng-drag-data="{source: 'MaterialOrder', index: $index, item: (materialOrder.items.length>1?1:0)}" data-allow-transform="true" ng-center-anchor="true">
                   {{materialOrder.items[materialOrder.items.length>1?1:0]}}
                </div>
              </div>
            </div>
            
          </div>
        </div>
        
      </div>
    </div>
    
    <div layout="column" layout-align="space space-between" class="right">
      <div flex layout="row">
        <div ng-repeat="inventory in ll.productsInventory" layout="column" layout-align="center center" ng-drop="true" ng-drop-success="ll.onDropOnProductInventory($data,$index,$event)" class="production ng-class:inventory.product.CP_Name;">
          <div class="title">
            {{inventory.product.CP_Name}}产品库
          </div>
          <img class="inventoryIcon" src="assets/logistic/product.png"/>
          <div class="number" ng-drag="true" ng-drag-data="{source: 'ProductInventory', index: $index, num: inventory.num}" data-allow-transform="true" ng-center-anchor="true">
            {{inventory.num}}
          </div>
          
        </div>
      </div>
      
      <div layout="row" layout-align="end">
        <div ayout="column" class="actionColumn" >
          <div class="buyButtonWrapper">
            <md-button class="buyButton" ng-click="ll.onEmergencyBuyClicked($event)" aria-label="Action">
              紧急采购
            </md-button>
          </div>
          <div class="buyButtonWrapper">
            <md-button class="buyButton" ng-click="ll.onSellInventoryClicked($event)" aria-label="Action">
              出售库存
            </md-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div layout="row" layout-align="center">
    <img class="titleText" src="assets/logistic/title.png">  
  </div>
  
</div>