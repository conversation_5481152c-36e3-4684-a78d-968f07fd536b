'use strict';

angular
    .module('entrepApp.logistic', ['ngMaterial', 'ngDraggable', 'entrepApp.core'])
    .controller('LogisticController', [
      'appCache', 'UserService', '$mdPanel', '$rootScope', '$log', LogisticController
    ])
    .controller('OrderMaterialController', [
      'appCache', 'UserService', '$mdPanel' , '$log', OrderMaterialController
    ])
    .controller('EmergencyBuyDialogController', [
      'appCache', 'UserService', '$rootScope', '$log', EmergencyBuyDialogController
    ])
    .controller('SellInventoryDialogController', [
      'appCache', 'UserService', '$log', SellInventoryDialogController
    ]);

function LogisticController(appCache, UserService, $mdPanel, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;
  self.materialsInventory = appCache.calculatedParams.materialsInventory;
  self.materialOrders = appCache.calculatedParams.materialOrders;
  self.moveMaterialOrders = appCache.moveState.materialOrders;
  self.productsInventory = appCache.calculatedParams.productsInventory;
  self.moveProducts = appCache.moveState.products;

  self.onMaterialOrderClicked = onMaterialOrderClicked;
  self.onDropOnTruck = onDropOnTruck;
  self.onDropOnMaterialInventory = onDropOnMaterialInventory;
  self.onDropOnProductInventory = onDropOnProductInventory;
  self.onEmergencyBuyClicked = onEmergencyBuyClicked;
  self.onSellInventoryClicked = onSellInventoryClicked;
  self.showConfirmMaterialNum = false

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  ];

  function onMaterialOrderClicked(index, ev) {
    if (!UserService.checkPermission('Buy_Material')) {
      return;
    }
    
    var dlg = {
      controller: 'OrderMaterialController as ll',
      templateUrl: 'app/logistic/orderMaterial.tmpl.html',
      attachTo: angular.element(document.querySelector('.logisticContainerCover')),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        currentMaterialOrder: self.materialOrders[index],
        wheels: self.wheels
      }
    };

    appCache.showLogisticCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function onDropOnTruck(data, destIndex, ev) {
    if (!UserService.checkPermission('Update_Material')) {
      return;
    }
    if (data.source != 'MaterialOrder') {
      return;
    }
    if (data.index != destIndex) {
      return;
    }
    var materialOrder = self.materialOrders[destIndex];
    if (materialOrder.items[0] != 0 || materialOrder.items[1] == 0) {
      return;
    }
    if (!self.moveMaterialOrders[destIndex]) {
      self.moveMaterialOrders[destIndex] = [];
    }
    self.moveMaterialOrders[destIndex][1] = true;
    UserService.updateCalculatedParams();
    _checkMaterialOrderMove();
  }

  function onDropOnMaterialInventory(data, destIndex, ev) {
    if (!UserService.checkPermission('Update_Material')) {
      return;
    }
    if (data.source != 'MaterialOrder' && data.source != 'MaterialTruck') {
      return;
    }
    if (data.index != destIndex) {
      return;
    }
    if (data.item != 0) {
      return;
    }
    var materialOrder = self.materialOrders[destIndex];
    if (materialOrder.items[0] == 0) {
      return;
    }
    if (!self.moveMaterialOrders[destIndex]) {
      self.moveMaterialOrders[destIndex] = [];
    }
    self.moveMaterialOrders[destIndex][0] = true;
    UserService.updateCalculatedParams();
    _checkMaterialOrderMove();
  }

  function onDropOnProductInventory(data, index, ev) {
    if (!UserService.checkPermission('Update_Produce')) {
      return;
    }
    if (data.source != 'Product') {
      return;
    }
    if (data.index != 0) {
      return;
    }
    var productID = data.productLine.pl.PL_CPID;
    if (productID != index + 1) {
      return;
    }

    var wsIndex = data.productLine.pl.PL_WID - 1;
    var plIndex = data.productLine.pl.PL_ID - 1;
    if (!self.moveProducts[wsIndex]) {
      self.moveProducts[wsIndex] = [];
    }
    self.moveProducts[wsIndex][plIndex] = true;
    UserService.updateCalculatedParams();

    _checkProductMove();
  }

  function onEmergencyBuyClicked(ev) {
    var dlg = {
      controller: 'EmergencyBuyDialogController as ll',
      templateUrl: 'app/logistic/emergencyBuyDialog.html',
      attachTo: angular.element(document.querySelector('.logisticContainerCover')),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        request: {
          userID: appCache.userID,
          action: 'Emergency_Buy',
          params: {
            material: [0 ,0, 0, 0],
            product: [0 ,0, 0, 0]
          }
        }
      }
    };

    appCache.showLogisticCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function onSellInventoryClicked(ev) {
    var dlg = {
      controller: 'SellInventoryDialogController as ll',
      templateUrl: 'app/logistic/sellInventoryDialog.html',
      attachTo: angular.element(document.querySelector('.logisticContainerCover')),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        request: {
          userID: appCache.userID,
          action: 'Sell_Inventory',
          params: {
            material: [],
            product: []
          }
        }
      }
    };

    appCache.showLogisticCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function _checkMaterialOrderMove() {
    if (!appCache.moveState.materialOrderMoveCompleted) {
      return;
    }
    self.moveMaterialOrders.splice(0, self.moveMaterialOrders.length);

    var totalFee = 0;
    for (var index = 0; index < appCache.user.orderMaterials.length; ++index) {
      var orderMaterial = appCache.user.orderMaterials[index];
      var materialCode = appCache.system.codes.materials[orderMaterial.OM_CMID - 1];
      if (orderMaterial.OM_Remain_Date == 1) {
        totalFee += orderMaterial.OM_Num * materialCode.CM_Buy_Fee;  
      }
    }

    if (totalFee > 0) {
      var title = '申请购买原材料';
      var content = '购买原材料费用: ' + totalFee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Update_Material',
          params: {}
        }
      });
    } else {
      UserService.requestAction({
        userID: appCache.userID,
        action: 'Update_Material',
        params: {}
      }, function() {
        $log.info('Request completed.');
      }, function(e) {
        $log.error(e.data);
      });
    }
  }

  function _checkProductMove() {
    if (!appCache.moveState.productMoveCompleted) {
      return;
    }
    self.moveProducts.splice(0, self.moveProducts.length);
    UserService.requestAction({
      userID: appCache.userID,
      action: 'Update_Produce',
      params: {}
    }, function() {
      $log.info('Request completed.');
    }, function(e) {
      $log.error(e.data);
    });
  }
}

function OrderMaterialController(appCache, UserService, $mdPanel, $log) {
  var self = this;

  //self.materialOrderNum = 0;

  self.cancelMaterialOrder = cancelMaterialOrder;
  self.confirmMaterialOrder = confirmMaterialOrder;
  self.confirmMaterialNum = confirmMaterialNum;

  function cancelMaterialOrder() {
    self.mdPanelRef.close().then(function() {
      appCache.showLogisticCover = false;
    });
  };
  // 确认下原料订单  数量
  function confirmMaterialNum() {
    // show the confirm dialog
    var title = '下原料订单' + self.currentMaterialOrder.material.CM_Name;
    var content = '订购数量:' + self.materialOrderNum;
    var dlg = {
      controller: 'OrderMaterialController as ll',
      templateUrl: 'app/logistic/logisticConfirm.tmpl.html',
      attachTo: angular.element(document.querySelector('.logisticContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        title, content,
        currentMaterialOrder: self.currentMaterialOrder,
        materialOrderNum: self.materialOrderNum
      }
    };
    self.showConfirmMaterialNum = true
    appCache.logisticContainerCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      
    });
  }

 
  
  function confirmMaterialOrder(materialOrderNum) {
    self.mdPanelRef.close().then(function() {
      UserService.requestAction({
        userID: appCache.userID,
        action: 'Buy_Material',
        params: { 
          OM_CMID: self.currentMaterialOrder.material.CM_ID,
          OM_Num: materialOrderNum,
          OM_Remain_Date: self.currentMaterialOrder.material.CM_Lead_Date,
          OM_Add_Date: appCache.user.date 
        }
      }, function() {
        $log.info('Buy_Material.');
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showLogisticCover = false;
    });
  };
}

function EmergencyBuyDialogController(appCache, UserService, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;
  self.materialsInventory = appCache.calculatedParams.materialsInventory;
  self.productsInventory = appCache.calculatedParams.productsInventory;

  self.currentValue = 0;
  self.currentItem = 0;
  self.productActive = false;

  self.cancel = cancel
  self.confirm = confirm;

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  ];

  for (var i = 0; i < self.materialsInventory.length; ++i) {
    self.request.params.material.push(0);
  }
  for (var i = 0; i < self.productsInventory.length; ++i) {
    self.request.params.product.push(0);
  }

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showLogisticCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {

      var totalFee = 0;
      if (self.productActive) {
        for (var i = 0; i < self.productsInventory.length; ++i) {
          var inventory = self.productsInventory[i];
          var fee = inventory.product.CP_Direct_Cost * self.appCache.system.settings.Emergence_Multiple_Product
          totalFee += fee * self.request.params.product[i];
        }
      } else {
        for (var i = 0; i < self.materialsInventory.length; ++i) {
          var inventory = self.materialsInventory[i];
          var fee = inventory.material.CM_Buy_Fee * self.appCache.system.settings.Emergence_Multiple_Material
          totalFee += fee * self.request.params.material[i];
        }
      }
      var title = '申请紧急采购' + (self.productActive ? '成品' : '原材料');
      var content = '紧急采购费用: ' + totalFee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: self.request
      });
      
      appCache.showLogisticCover = false;
    });
  };
}

function SellInventoryDialogController(appCache, UserService, $log) {
  var self = this;

  self.appCache = appCache;
  self.materialsInventory = [];
  self.productsInventory = [];

  self.currentValue = 0;
  self.currentItem = 0;

  self.cancel = cancel
  self.confirm = confirm;

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  ];

  for (var index = 0; index < appCache.calculatedParams.materialsInventory.length; ++index) {
    var inventory = appCache.calculatedParams.materialsInventory[index];
    if (inventory.num > 0) {
      self.materialsInventory.push(inventory);
      self.request.params.material.push({ID: inventory.material.CM_ID, num: 0});
    }
  }
  for (var index = 0; index < appCache.calculatedParams.productsInventory.length; ++index) {
    var inventory = appCache.calculatedParams.productsInventory[index];
    if (inventory.num > 0) {
      self.productsInventory.push(inventory);
      self.request.params.product.push({ID: inventory.product.CP_ID, num: 0});
    }
  }

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showLogisticCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      UserService.requestAction(self.request, function() {
        $log.info('Request completed.');
      }, function(e) {
        $log.error(e.data);
      });
      
      appCache.showLogisticCover = false;
    });
  };
}