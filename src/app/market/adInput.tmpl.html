
<hm-dir rotate-matrix="[0,-1,1,0]" init-size="[500,330]" ng-cloak>

  <form>

    <div class="inputDlgHead">
      <h4>投放广告</h4>
    </div>

    <div flex layout-padding>

      <table class="table table-striped table-hover table-bordered">
        <thead>
          <tr>
            <th ng-repeat="headItem in ml.inputHead" >{{headItem.name}}</th>
          </tr>
        </thead>
          <tr ng-repeat="row in ml.inputTable">
            <td ng-repeat="item in row" ng-click="ml.showInput=(item.input&&!item.readonly);ml.currentItem=item;ml.currentValue=item.value" ng-class="{activeItem:(item.input&&!item.readonly)}" >{{item.input?item.value:item.name}}</td>
          </tr>
        <tbody>
        </tbody>
      </table>

    </div>

    <div layout="row" layout-align="center" class="inputActions">
      <md-button class="md-raised md-primary" ng-click="ml.confirm()" md-autofocus>
        确认
      </md-button>
      <md-button ng-click="ml.cancel()">
        取消
      </md-button>
    </div>

    <div ng-if="ml.showInput" class="inputContainer">
      <div data-no-move="true" class="selectContainer">
        <input ng-model="ml.currentValue" class="inputSelect" />
        <num-select wheels="ml.wheels" amount="ml.currentValue" rotate-matrix="[0,-1,1,0]"></num-select>
        <div layout="row" layout-align="center center">
           <md-button class="md-raised md-primary" ng-click="ml.showInput=false;ml.currentItem.value=ml.currentValue;" md-autofocus>
            确认
          </md-button>
        </div>
      </div>
    </div>
  </form>

</hm-dir>