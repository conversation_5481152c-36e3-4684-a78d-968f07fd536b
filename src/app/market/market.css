

.marketRegion .titleText {
  position: absolute;
  bottom: -5px;
  left: 380px;
}

.marketRegion .orderArea {
  margin-bottom: 50px;
}

.marketRegion .orderArea .order {
  background: #D6E26E;
  border: 1px solid #ACC883;
  border-radius: 6px;
  margin: 10px;
}

.marketRegion .order .title {
  margin-bottom: 10px;
  color: white;
}

.marketRegion .order .value {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: white;
  line-height: 30px;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 90%;
  text-align: center;
  font-size: 15px;
  color: #FCAC12;
}

.marketRegion .quantityArea {
  margin-bottom: 50px;
}

.marketRegion .investBtn {
  color: #7CFC00;
}

.marketRegion .investIcon {
  color: #1AB2A5;
  margin-bottom: 20px;
}

.marketRegion .title {
  margin-bottom: 15px;
}

.marketRegion .item {
  background: grey;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0px 3px 0px 0px;
}

.marketRegion .lightItem {
  background: #1AB2A5;
}

.marketRegion .quantityArea .productQuantity {
  padding-top: 10px;
  color: white;
  margin: 10px;
  text-align: center;

  background: #2AD8A2;
  border: 1px solid #16B382;
  border-radius: 6px;
}

.marketRegion .quantityArea .marketQuantity {
  padding-top: 10px;
  color: white;
  margin: 10px;
  text-align: center;
  background: #7FA9E6;
  border: 1px solid #4487E9;
  border-radius: 6px;
}

.marketRegion .actionContainer {
  margin-bottom: 15px;
  padding: 0px;
  background: #FFFFFF;
  box-shadow: 0 1px 1px 0 rgba(0,0,0,0.16);
  border-radius: 47px;
}

.marketRegion .payButton {
  padding-left: 20px;
  padding-right: 20px;
  font-family: DFFangYuanW7;
  font-size: 18px;
  color: #FFFFFF;
  text-shadow: 0 1px 1px rgba(0,0,0,0.27);

  margin: 3px;
  background: #FF820A;
  border: 1px solid #FF6060;
  box-shadow: 0 -1px 4px 0 rgba(169,92,33,0.32), inset 0 2px 5px 1px rgba(245,92,46,0.75);
  border-radius: 47px;
}

.marketRegion .isoQuantity {
  color: white;
  margin: 10px 15px 0px 10px;
  text-align: center;
  padding-top: 15px;

  background: #FCAC12;
  border: 1px solid #E59D11;
  border-radius: 100px;
}

.marketContainerCover .inputDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.marketContainerCover .inputDlgHead small {
  margin-left: 40px;
}

.marketContainerCover form {
  background-color: white;
}

.marketContainerCover table th {
  text-align: center;
}

.marketContainerCover table td {
  text-align: center;
}

.marketContainerCover .activeItem {
  color: green;
}

.marketContainerCover .inputContainer {
  left: 0px;
  top: 0px;
  position: absolute;
  width: 100%;
  height: 100%;
  background: grey;
}

.marketContainerCover .inputSelect {
  width: 100%;
  text-align: center;
  background: yellow;
  color: black;
}

.marketContainerCover .selectContainer {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}

.marketContainerCover .showOrder .orderItem {
  margin: 0px 10px 5px 0px;
  background: #F0F0F0;
  padding: 8px;
  border-radius: 10px;
}

.marketContainerCover .showOrder .orderItem .priceItem {
  font-size: 120%;
  color: blue;
  font-weight: bold;
}

.marketContainerCover .showOrder .orderUnder {
  width: 100%;
}

.marketContainerCover .showOrder .orderUnder .item {
  margin-right: 6px;
}

.marketContainerCover .showOrder .orderUnder .item .value {
  color: blue;
  font-weight: bold;
}

.marketContainerCover .predictionTabs {
  min-height: 800px !important;
  height: 100% !important;
}

.marketContainerCover .verticalCenter {
  vertical-align: middle !important;
}