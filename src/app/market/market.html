<div layout-fill layout="column" class="marketRegion" ng-controller="MarketController as ml">
  
  <div flex layout="row">
    <div flex="25" layout="column" class="orderArea">
      <div flex layout="row" layout-wrap>
        <div ng-repeat="orders in ml.appCache.calculatedParams.productOrders" flex="40" layout="column" layout-align="end center" ng-click="ml.onShowOrder($index,$event)" class="order">
          <img class="orderIcon" src="assets/logistic/order.png"/>
          <div class="value">{{orders.length}}张</div>
          <div class="title">{{ml.appCache.system.codes.products[$index].CP_Name}}订单</div>
        </div>
      </div>
    </div>
    <div flex="50" layout="column" class="quantityArea" >
      <div flex layout="row">
        <div flex ng-repeat="productDevelop in ml.appCache.calculatedParams.productDevelops" layout="column" layout-align="space-between center" class="productQuantity">
          <div class="title">{{productDevelop.productName}}<br/>生产资格</div>
          <div layout="column" layout-align="end center">
            <div ng-if="!productDevelop.DP_Finish_Date" layout="column" layout-align="center center" >
              <div layout="row" class="indicate">
                <div ng-repeat="di in productDevelop.items track by $index" class="item" ng-class="{lightItem: di}"></div>
              </div>
              <md-button class="investBtn" ng-click="ml.onInvestProduct($index,$event)" ng-disabled="productDevelop.DP_Last_Date==ml.appCache.user.date">投资</md-button>
            </div>
            
            <md-icon md-svg-icon="star" class="investIcon" ng-if="productDevelop.DP_Finish_Date"></md-icon>
          </div>
        </div>
      </div>
      <div flex layout="row">
        <div flex ng-repeat="marketDevelop in ml.appCache.calculatedParams.marketDevelops" layout="column" layout-align="space-between center" class="marketQuantity">
          <div class="title">{{marketDevelop.marketName}}<br/>市场准入</div>
          <div layout="column" layout-align="end center">
            <div ng-if="!marketDevelop.DM_Finish_Date" layout="column" layout-align="center center" >
              <div layout="row" class="indicate">
                <div ng-repeat="mi in marketDevelop.items track by $index" class="item" ng-class="{lightItem: mi}"></div>
              </div>
              <md-button class="investBtn" ng-click="ml.onInvestMarket($index,$event)" ng-disabled="marketDevelop.DM_Last_Date==ml.appCache.user.date">投资</md-button>
            </div>
            
            <md-icon md-svg-icon="star" class="investIcon" ng-if="marketDevelop.DM_Finish_Date"></md-icon>
          </div>
          
        </div>
      </div>
    </div>
    <div flex layout="column" layout-align="space-between end">
      <div layout="row">
        <div flex ng-repeat="isoDevelop in ml.appCache.calculatedParams.isoDevelops" layout="column" layout-align="space-between center" class="isoQuantity">
          <div class="title">{{isoDevelop.isoName}}<br/>资格</div>
          <div layout="column" layout-align="end center">
            <div ng-if="!isoDevelop.DI_Finish_Date" layout="column" layout-align="center center" >
              <div layout="row" class="indicate">
                <div ng-repeat="ii in isoDevelop.items track by $index" class="item" ng-class="{lightItem: ii}"></div>
              </div>
              <md-button class="investBtn" ng-click="ml.onInvestISO($index,$event)" ng-disabled="isoDevelop.DI_Last_Date==ml.appCache.user.date">投资</md-button>
            </div>

            <md-icon md-svg-icon="star" class="investIcon" ng-if="isoDevelop.DI_Finish_Date"></md-icon>
          </div>
          
        </div>
      </div>
      
      <div layout="column">
        <div layout="column" class="actionContainer">
          <md-button class="payButton" ng-click="ml.onMarketPrediction()" aria-label="Action">
            市场预测
          </md-button>
        </div>
        <div layout="column" class="actionContainer">
          <md-button class="payButton" ng-click="ml.onPayAD($event)" aria-label="Action">
            投放广告
          </md-button>
        </div>
      </div>
      
    </div>
  </div>

  <img class="titleText" src="assets/market/title.png">
</div>