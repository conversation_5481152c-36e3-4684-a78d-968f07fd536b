'use strict';

angular
    .module('entrepApp.market', ['ngMaterial', 'ngDraggable', 'entrepApp.core'])
    .controller('MarketController', [
      'appCache', 'UserService', '$mdPanel', '$rootScope', '$log', MarketController
    ])
    .controller('ADInputController', [
      'appCache', 'UserService', '$rootScope', '$log', ADInputController
    ])
    .controller('MarketOrderController', [
      'appCache', 'UserService', '$rootScope', '$log', MarketOrderController
    ])
    .controller('MarketPredictionController', [
      'appCache', 'UserService', '$rootScope', '$log', MarketPredictionController
    ]);

function MarketController(appCache, UserService, $mdPanel, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;

  self.onInvestProduct = onInvestProduct;
  self.onInvestMarket = onInvestMarket;
  self.onInvestISO = onInvestISO;
  self.onPayAD = onPayAD;
  self.onShowOrder = onShowOrder;
  self.onMarketPrediction = onMarketPrediction;

  function onInvestProduct(index, ev) {
    if (!UserService.checkPermission('Develop_Product')) {
      return;
    }

    var produceCode = appCache.system.codes.products[index];
    var title = '申请投资产品研发';
    var content = '投资产品研发费用: ' + produceCode.CP_Develop_Fee + 'M';

    $rootScope.$broadcast('event:paymentRequired', {
      title,
      content,
      request: {
        userID: appCache.userID,
        action: 'Develop_Product',
        params: {
          ID: index + 1
        }
      }
    });
  }

  function onInvestMarket(index, ev) {
    if (!UserService.checkPermission('Develop_Market')) {
      return;
    }

    var marketCode = appCache.system.codes.markets[index];
    var title = '申请投资市场开发';
    var content = '投资市场开发费用: ' + marketCode.CM_Develop_Fee + 'M';

    $rootScope.$broadcast('event:paymentRequired', {
      title,
      content,
      request: {
        userID: appCache.userID,
        action: 'Develop_Market',
        params: {
          ID: index + 1
        }
      }
    });
  }

  function onInvestISO(index, ev) {
    if (!UserService.checkPermission('Develop_ISO')) {
      return;
    }

    var isoCode = appCache.system.codes.isos[index];
    var title = '申请投资ISO资格';
    var content = '投资ISO资格费用: ' + isoCode.CI_Develop_Fee + 'M';

    $rootScope.$broadcast('event:paymentRequired', {
      title,
      content,
      request: {
        userID: appCache.userID,
        action: 'Develop_ISO',
        params: {
          ID: index + 1
        }
      }
    });
  }

  function onPayAD(ev) {
    if (!UserService.checkPermission('Pay_AD')) {
      return;
    }

    var dlg = {
      controller: 'ADInputController as ml',
      templateUrl: 'app/market/adInput.tmpl.html',
      attachTo: angular.element(document.querySelector('.marketContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {}
    };

    appCache.showMarketCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function onShowOrder(index, ev) {
    var dlg = {
      controller: 'MarketOrderController as ml',
      templateUrl: 'app/market/order.tmpl.html',
      attachTo: angular.element(document.querySelector('.marketContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: { productIndex : index }
    };

    appCache.showMarketCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function onMarketPrediction(){
    var dlg = {
      controller: 'MarketPredictionController as ml',
      templateUrl: 'app/market/prediction.tmpl.html',
      attachTo: angular.element(document.querySelector('.marketContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {}
    };

    appCache.showMarketCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }
}

function ADInputController(appCache, UserService, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;
  self.inputHead = [{name: '产品\\市场'}];
  self.inputTable = [];
  self.showInput = false;
  self.currentItem = null;
  self.currentValue = 0;

  self.cancel = cancel
  self.confirm = confirm;

  var marketCodes = appCache.system.codes.markets;
  var productCodes = appCache.system.codes.products;
  var marketDevelops = appCache.calculatedParams.marketDevelops;
  for (var index = 0; index < marketCodes.length; ++index) {
    var marketCode = marketCodes[index];
    self.inputHead.push({name: marketCode.CM_Name});
  }
  for (var index = 0; index < productCodes.length; ++index) {
    var productCode = productCodes[index];
    self.inputTable.push([{ input:false, name: productCode.CP_Name }]);
    for (var k = 0; k < marketCodes.length; ++k) {
      var marketDevelop = marketDevelops[k];
      self.inputTable[index].push({input: true, value: 0, readonly: !marketDevelop.DM_Finish_Date});
    }
  }

  self.wheels = [
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }, 
    {
      circular: true,
      data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  ];

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showMarketCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {

      var totalFee = 0;
      var items = [];
      for (var m = 0; m < self.inputTable.length; ++m) {
        var row = self.inputTable[m];
        for (var n = 0; n < row.length; ++n) {
          var item = self.inputTable[m][n];
          if (item.input && item.value > 0) {
            totalFee += item.value;
            items.push({ PA_Fee: item.value, PA_CPID: m + 1, PA_CMID: n });
          }
        }
      }

      var title = '申请支付广告费';
      var content = '支付广告费: ' + totalFee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Pay_AD',
          params: { PA_Date: appCache.user.date, items }
        }
      });

      appCache.showMarketCover = false;
    });
  };
}

function MarketOrderController(appCache, UserService, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;
  self.orders = appCache.calculatedParams.productOrders[self.productIndex];

  self.finishedOrders = [];
  self.unfinishedOrders = [];

  self.cancel = cancel;
  self.onDropOnUnFinishedOrder = onDropOnUnFinishedOrder;

  for (let index = 0; index < self.orders.length; ++index) {
    let order = self.orders[index];
    if (order.order.OP_Status == 'Unfinished') {
      self.unfinishedOrders.push(order);
    } else {
      self.finishedOrders.push(order);
    }
  }

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showMarketCover = false;
    });
  };

  function onDropOnUnFinishedOrder(data, destIndex, ev) {
    if (!UserService.checkPermission('Sell_Product')) {
      return;
    }
    if (data.source != 'ProductInventory') {
      return;
    }

    var order = self.unfinishedOrders[destIndex];
    if (order.code.CO_CPID != data.index + 1 || order.code.CO_Num > data.num) {
      return;
    }

    UserService.requestAction({
      userID: appCache.userID,
      action: 'Sell_Product',
      params: { ID: order.code.CO_ID }
    }, function () {
      $log.info('Request completed.');
      cancel()
    }, function (e) {
      cancel()
      $log.error(e.data);
    });
  }
}

function MarketPredictionController(appCache, UserService, $rootScope, $log) {

  var self = this;
  self.markets = appCache.system.codes.markets;
  self.appCache = appCache;
  self.orderdata = {};
  self.scroll = 0;
  
  self.onTableScroolEnd = onTableScroolEnd
  self.onTableMove = onTableMove;
  self.closeDlg = closeDlg;
  self.getOption = getOption;

  self.length = appCache.system.codes.products.length;
  var years = [];
  appCache.system.codes.orders.forEach(order => {
    if (!self.orderdata[order.CO_Year]) {
      years.push(order.CO_Year);
      var products = {};
      appCache.system.codes.products.forEach(product => {
        var details = {};
        appCache.system.codes.markets.forEach(market => {
          details[market.CM_ID] = {
            name: market.CM_Name,
            price: 0,
            productNum: 0,
            num: 0,
          }
        })
        products[product.CP_ID] = { name: product.CP_Name, details: details };
      })
      self.orderdata[order.CO_Year] = { year: order.CO_Year, products: products };
    }
    self.orderdata[order.CO_Year].products[order.CO_CPID].details[order.CO_CMID].price += order.CO_Total;
    self.orderdata[order.CO_Year].products[order.CO_CPID].details[order.CO_CMID].productNum += order.CO_Num;
    self.orderdata[order.CO_Year].products[order.CO_CPID].details[order.CO_CMID].num++;
  });



  function getOption(productID, productName) {
    var series = [];
    var AveragePrice = [];
    var Requirement = [];
    var OrdersNumber = [];
    var marketNames = [];
    var title = '';
    appCache.system.codes.markets.forEach((market, marketindex) => {
      years.forEach(year => {
        let detail = self.orderdata[year].products[productID].details[market.CM_ID];
        if (marketNames.length === marketindex) {
          marketNames.push(detail.name)
          AveragePrice.push({
            name: detail.name,
            data: [(detail.price / detail.productNum).toFixed(2)],
            type: "line"
          })
          Requirement.push({
            name: detail.name,
            data: [detail.productNum],
            type: "line"
          })
          OrdersNumber.push({
            name: detail.name,
            data: [detail.num],
            type: "line"
          })
        } else {
          AveragePrice[marketindex].data.push((detail.price / detail.productNum ? detail.price / detail.productNum : 0).toFixed(2));
          Requirement[marketindex].data.push(detail.productNum);
          OrdersNumber[marketindex].data.push(detail.num);
        }
      })
    })

    switch (self.tab) {
      case 'AveragePrice':
        series = AveragePrice;
        title = productName + '均价图'
        break;
      case 'Requirement':
        series = Requirement;
        title = productName + '需求量图'
        break;
      case 'OrdersNumber':
        series = OrdersNumber;
        title = productName + '订单数图'
        break;
    }
    if (series.length > 0) {
      return {
        title: {
          text: title,
          x: 'center',
          y: '85%',
        },
        legend: {
          data: marketNames,
        },
        xAxis: {
          type: 'category',
          data: years
        },
        yAxis: {
          type: "value",
          min: function(value){
            return Math.floor(value.min);
          },
          max: function(value){
            return Math.ceil(value.max);
          }
        },
        series: series
      };
    }

  }


  function closeDlg() {
    self.mdPanelRef.close().then(function () {
      appCache.showMarketCover = false;
    });
  }

  function onTableMove(event){
    var element=document.querySelector('.prediction-hm-dir md-tab-content.md-active');
    element.scrollTop =self.scroll-event.deltaX;
  }

  function onTableScroolEnd(event){
    self.scroll+=-event.deltaX;
  }

}