
<hm-dir rotate-matrix="[0,-1,1,0]" init-size="[1000,800]" ng-cloak class="prediction-hm-dir">

    <form>
  
      <div class="inputDlgHead" layout="row">
        <h4>市场预测</h4>
        <span flex></span>
        <md-button class="md-icon-button" ng-click="ml.closeDlg()">
          <md-icon md-svg-icon="close" aria-label="Close dialog"></md-icon>
        </md-button>
      </div>
  
      <div
        layout-padding
        layout="row"
      >
      <div flex="60">
        <md-tabs
          class="predictionTabs"
          md-border-bottom
          md-selected="0"
          md-stretch-tabs="always"
        >
          <md-tab label="均价" md-on-select="ml.tab = 'AveragePrice';ml.scroll=0">
            <table class="table table-striped table-hover table-bordered" data-no-move="true"  hm-panmove="ml.onTableMove($event)" hm-panend="ml.onTableScroolEnd($event)">
              <thead>
                <tr>
                  <th>年份</th>
                  <th>产品</th>
                  <th ng-repeat="market in ml.markets">{{market.CM_Name}}</th>
                </tr>
              </thead>
              <tbody ng-repeat="data in ml.orderdata">
                <tr ng-repeat="product in data.products">
                  <td
                    rowspan="{{ml.length}}"
                    ng-if="$index==0"
                    class="verticalCenter"
                  >第{{data.year}}年</td>
                  <td>{{product.name}}</td>
                  <td ng-repeat="detail in product.details">{{detail.price/detail.productNum?(detail.price/detail.productNum |
                    number:2):0}}</td>
                </tr>
              </tbody>
            </table>
          </md-tab>
          <md-tab label="需求量" md-on-select="ml.tab = 'Requirement';ml.scroll=0">
            <table class="table table-striped table-hover table-bordered" data-no-move="true"  hm-panmove="ml.onTableMove($event)" hm-panend="ml.onTableScroolEnd($event)">
              <thead>
                <tr>
                  <th>年份</th>
                  <th>产品</th>
                  <th ng-repeat="market in ml.markets">{{market.CM_Name}}</th>
                </tr>
              </thead>
              <tbody ng-repeat="data in ml.orderdata">
                <tr ng-repeat="product in data.products">
                  <td
                    rowspan="{{ml.length}}"
                    ng-if="$index==0"
                    class="verticalCenter"
                  >第{{data.year}}年</td>
                  <td>{{product.name}}</td>
                  <td ng-repeat="detail in product.details">{{detail.productNum}}</td>
                </tr>
              </tbody>
            </table>
          </md-tab>
          <md-tab label="订单数" md-on-select="ml.tab = 'OrdersNumber';ml.scroll=0">
            <table class="table table-striped table-hover table-bordered" data-no-move="true"  hm-panmove="ml.onTableMove($event)" hm-panend="ml.onTableScroolEnd($event)">
              <thead>
                <tr>
                  <th>年份</th>
                  <th>产品</th>
                  <th ng-repeat="market in ml.markets">{{market.CM_Name}}</th>
                </tr>
              </thead>
              <tbody ng-repeat="data in ml.orderdata">
                <tr ng-repeat="product in data.products">
                  <td
                    rowspan="{{ml.length}}"
                    ng-if="$index==0"
                    class="verticalCenter"
                  >第{{data.year}}年</td>
                  <td>{{product.name}}</td>
                  <td ng-repeat="detail in product.details">{{detail.num}}</td>
                </tr>
              </tbody>
            </table>
          </md-tab>
        </md-tabs>
        </div>
        <div layout="column" flex="40">
          <div ng-repeat="product in ml.appCache.system.codes.products" e-chart ec-data="ml.getOption(product.CP_ID,product.CP_Name)" style='height: 25%;'></div>
        </div>    
      </div>
  
    </form>
  
  </hm-dir>