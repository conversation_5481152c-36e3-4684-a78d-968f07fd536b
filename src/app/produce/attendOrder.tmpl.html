
<div class="attendOrderContainer">
    <div ng-if="pl.status==2" class="attendOrderActive">
      <md-content layout-fill>
        <div layout="row" layout-fill">
          <div class="panel panel-success left" flex="45">
            <div class="panel-heading title">
              <div>{{'第'+pl.systemYear+'年订货会('+pl.system.codes.markets[pl.rounds[0].market-1].CM_Name+','+pl.system.codes.products[pl.rounds[0].product-1].CP_Name+')用户('+pl.user.username+')选单情况及选单顺序'}}</div>
            </div>
            <div class="panel-body">
              <table class="table table-striped table-hover table-bordered">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>用户</th>
                    <th>产品广告额</th>
                    <th>市场广告额</th>
                    <th>去年销售额</th>
                    <th>去年违约情况</th>
                    <th>剩余选单次数</th>
                    <th>剩余选单时间</th>
                    <th>当前状态</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="team in pl.rounds[0].teams" >
                    <td>{{team.userID}}</td>
                    <td>{{team.username}}</td>
                    <td>{{team.productAD+'M'}}</td>
                    <td>{{team.marketAD+'M'}}</td>
                    <td>{{team.sales+'M'}}</td>
                    <td>{{team.breach?'有':'无'}}</td>
                    <td>{{team.remainOrderCount}}</td>
                    <td>{{team.countdown+'秒'}}</td>
                    <td>{{team.countdown?'选单':'等待'}}</td>
                  </tr>
                </tbody>
              </table>

              <md-button ng-click="pl.onOrderClicked(-1)" class="button">跳过选单</md-button>
            </div>
          </div>
          <div class="panel panel-success right" ng-class="{selectActive: pl.teamCouldSelect}" flex>
            <div class="panel-heading title">
              <div>{{'参加第'+pl.systemYear+'年订货会('+pl.system.codes.markets[pl.rounds[0].market-1].CM_Name+','+pl.system.codes.products[pl.rounds[0].product-1].CP_Name+')'}}</div>
            </div>
            <div class="panel-body" layout="row" layout-wrap>
              <div layout="column" layout-align="space-between center" ng-click="pl.onOrderClicked($index)" ng-repeat="order in pl.rounds[0].orders" class="orderItem" ng-class="{orderDisableSelect: ((order.CO_ISO == 1 && !pl.ISO1Status) || (order.CO_ISO == 2 && !pl.ISO2Status))}">
                <label>{{'订单编号:'+order.CO_OrderID}}</label>
                <div layout="row">
                  <label>总价:</label><span class="priceItem">{{order.CO_Total+'M'}}</span>
                </div>
                <div layout="row" layout-align="space-around" class="orderUnder">
                  <div layout="column" class="item">
                    <label>数量</label>
                    <label class="value">{{order.CO_Num+'个'}}</label>
                  </div>
                  <div layout="column" class="item">
                    <label>交货期</label>
                    <label class="value">{{order.CO_Deliver_Date+'季'}}</label>
                  </div>
                  <div layout="column" class="item">
                    <label>账期</label>
                    <label class="value">{{order.CO_Receivable_Date+'季'}}</label>
                  </div>
                  <div layout="column" class="item">
                    <label>ISO</label>
                    <label class="value">{{order.CO_ISO?pl.system.codes.isos[order.CO_ISO-1].CI_Name:'否'}}</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
       </md-content>

      
    </div>

  

  <div ng-if="pl.status==0" class="attendOrderWaiting">
    <hm-dir init-size="[300,200]" >
      <form layout="column">

        <div layout="row" class="inputDlgHead">
          <h4>等待订货会开始</h4>
          <span flex></span>
          <md-button class="md-icon-button" ng-click="pl.cancel()">
            <md-icon md-svg-icon="close" aria-label="Close dialog"></md-icon>
          </md-button>
        </div>

        <div flex layout-padding>
          <div layout="row" layout-align="space-around">
            <md-progress-circular md-mode="indeterminate"></md-progress-circular>
          </div>
        </div>

      </form>    
    </hm-dir>
  </div>
  

  <div ng-if="pl.status==1" class="attendOrderFinished">

    <hm-dir init-size="[300,200]" >
      <form layout="column">

        <div class="inputDlgHead">
          <h4>订货会已结束</h4>
        </div>

        <div flex layout-padding>
          确认订货会已结束
        </div>

        <div layout="row" layout-align="center">
          <md-button class="md-raised md-primary" ng-click="pl.confirmOrderFinished()" md-autofocus>
            确认
          </md-button>
          <md-button ng-click="pl.cancel()">
            取消
          </md-button>
        </div>

      </form>

    </hm-dir>
    
  </div>
  
</div>