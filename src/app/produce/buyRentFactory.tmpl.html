
<hm-dir init-size="[300,230]" ng-cloak>

  <form ng-cloak name="buyRentForm" layout-fill layout="column">

    <div class="inputDlgHead">
      <h4>购买/租用厂房({{pl.currentWorkshop.code.CW_Name}})</h4>
    </div>

    <div flex layout-padding>
      <md-radio-group ng-model="pl.newFactory.W_Status" required>

        <md-radio-button value="Buy" class="md-primary">{{'购买('+pl.currentWorkshop.code.CW_Buy_Fee+'M)'}}</md-radio-button>
        <md-radio-button value="Rent">{{'租用('+pl.currentWorkshop.code.CW_Rent_Fee+'M)'}}</md-radio-button>

      </md-radio-group>
    </div>

    <div layout-padding layout="row" layout-align="center">
      <md-button class="md-raised md-primary" ng-disabled="!buyRentForm.$valid" ng-click="pl.confirmBuyRentFactory()" md-autofocus>
       确认
      </md-button>
      <md-button ng-click="pl.cancelBuyRentFactory()">
      取消
      </md-button>
    </div>

  </form>

</hm-dir>