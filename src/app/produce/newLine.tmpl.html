
<hm-dir init-size="[500,320]" ng-cloak>

  <form ng-cloak name="newLineForm" layout-fill layout="column">

    <div class="inputDlgHead">
      <h4>新建生产线投资</h4>
    </div>

    <div flex layout-padding>
      <label>生产线</label>
      <md-radio-group ng-model="pl.newLineId" layout="row" layout-wrap required>

        <md-radio-button flex="45" class="rowWrapRadioBtn" ng-repeat="lineCode in pl.system.codes.productLines" value="{{lineCode.CPL_ID}}">
          {{lineCode.CPL_Name+'(价格:'+lineCode.CPL_Buy_Fee*lineCode.CPL_Install_Date+'M)'}}
        </md-radio-button>

      </md-radio-group>
      <label>产品</label>
      <md-radio-group ng-model="pl.newLineProduct" layout="row" required>

        <md-radio-button ng-repeat="productCode in pl.system.codes.products" value="{{productCode.CP_ID}}">
          {{productCode.CP_Name}}
        </md-radio-button>

      </md-radio-group>
    </div>

    <div layout="row" layout-align="center">
      <md-button class="md-raised md-primary" ng-disabled="!newLineForm.$valid" ng-click="pl.confirmNewLine()" md-autofocus>
       确认
      </md-button>
      <md-button ng-click="pl.cancelNewLine()">
      取消
      </md-button>
    </div>

</form>

</hm-dir>