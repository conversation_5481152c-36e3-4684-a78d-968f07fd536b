
.produceRegion .left {
  padding: 5px 50px 0px 15px;
}

.produceRegion .right {
  padding: 5px 15px 0px 24px;
}

.produceRegion .titleRow {
  height: 30px;
}

.produceRegion .titleText {
  position: absolute;
  bottom: -8px;
}

.produceRegion .factory {
  font-family: DFFangYuanW7;
  font-size: 16px;
  color: #E1A258;
}

.produceRegion .factoryValue {
  margin-left: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #64ffda;
  line-height: 20px;
  font-size: 70%;
  text-align: center;
  opacity: 0.55;
  background: #CA6403;
  color: white;
}

.produceRegion .columnContainer {
  width: 85px;
  margin-top: 4px;
  margin-right: 0px;
}

.produceRegion .productLine {
  height: 175px;
  min-height: 175px;
  width: 100%;
  margin-bottom: 4px;
  text-align: center;

  background: #FDE3A3;
  border: 1px solid #FCDF93;
  border-radius: 8px;
}

.produceRegion .productLine .item {
  width: 80%;
  height: 30px;
  line-height: 25px;
  background: #aed581;
  margin-bottom: 10px;
  text-align: center;
  font-size: 80%;
}

.produceRegion .productLine .circleItem {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #dcedc8;
  line-height: 25px;
  text-align: center;
  margin-bottom: 15px;
  font-size: 70%;
}

.produceRegion .productLine .productItem {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: yellow;
  line-height: 20px;
  text-align: center;
}

.produceRegion .lineMark {
  width: 100%;
  height: 30px;
  background: #CA6403;
  line-height: 25px;
  text-align: center;
  margin-bottom: 8px;
  margin-left: 0px;
  color: white;
  font-size: 80%;
}

.produceRegion .lineValue {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  opacity: 0.55;
  font-size: 80%;
  color: white;
  background: #CA6403;
}

.produceRegion .lineNumber {
  color: #CA6403;
  margin-top: 8px;
  text-align: center;
}

.produceContainerCover .rowWrapRadioBtn {
  margin-bottom: 10px;
}

.produceContainerCover .inputDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.produceContainerCover form {
  background-color: white;
}

.produceContainerCover .attendOrderContainer {
  position: absolute;
  width: 100%;
  height: 100%;
}

.produceAttendOrderPanel {
  position: absolute;
  width: 100%;
  height: 100%;
}

.produceContainerCover .attendOrderActive {
  position: absolute;
  width: 100%;
  height: 100%;
}

.produceContainerCover .attendOrderActive .left {
  margin: 0px;
  height: 100%;
  font-size: 80%;
  text-align: center;
}

.produceContainerCover .attendOrderActive .right {
  margin: 0px;
  height: 100%;
  font-size: 80%;
  text-align: center;
}

.produceContainerCover .attendOrderActive .orderItem {
  margin: 0px 10px 5px 0px;
  background: #F5F5F5;
  padding: 8px;
  border-radius: 10px;
}

.produceContainerCover .attendOrderActive .orderItem .priceItem {
  font-size: 120%;
  color: red;
  font-weight: bold;
}

.produceContainerCover .attendOrderActive .orderUnder {
  width: 100%;
}

.produceContainerCover .attendOrderActive .orderUnder .item {
  margin-right: 6px;
}

.produceContainerCover .attendOrderActive .orderUnder .item .value {
  color: red;
  font-weight: bold;
}

.produceContainerCover .attendOrderActive .selectActive {
  background: #00f139;
}

.produceContainerCover .attendOrderActive .orderDisableSelect {
  background: #FFA500;
}

.produceContainerCover .attendOrderWaiting {
  width: 300px;
  height: 200px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}

.produceContainerCover .attendOrderFinished {
  width: 300px;
  height: 200px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}

.systemConfirmContainerCover .inputDlgHead {
  padding: 5px 0px 5px 10px;
  background: #CA6403;
  color: white;
}

.systemConfirmContainerCover form {
  background-color: white;
}