<div layout-fill layout="column" class="produceRegion" ng-controller="ProduceController as pl">
  <div layout="row">
    <div layout="column" layout-align="space-between" flex="60" id="produceRegionLeft" class="left">
      <div layout="row" layout-align="center center" class="titleRow">
        <md-menu>
          <div layout="row" ng-click="pl.onFactoryClicked(0,$mdMenu,$event)" class="factory">
            {{pl.workshops[0].code.CW_Name}} 
            <div class="factoryValue">{{pl.workshops[0].mark}}</div>
          </div>

          <md-menu-content width="2">
            <md-menu-item ng-repeat="processItem in pl.factoryProcessItems[0]">
              <md-button ng-click="pl.onFactoryProcess(0,processItem,$event)">
                {{processItem.name}}
              </md-button>
            </md-menu-item>
          </md-menu-content>

        </md-menu>
      </div>
      <div layout="row" flex layout-align="space-between">
        <div ng-repeat="line in pl.workshops[0].productLines" layout="column" layout-align="space-between center" class="columnContainer">
          <div flex layout="column" layout-align="end center" class="productLine">
            <!-- 投资中 -->
            <div ng-if="item.status==0||item.status==1" ng-repeat="item in line.items" class="circleItem">
              {{item.value}}
            </div>
            <!-- 空闲中 -->
            <div ng-if="item.status==2" ng-repeat="item in line.items" class="item" ng-drop="true" ng-drop-success="pl.onDropOnPrepareCell($data,line,$index,$event)">
              {{item.value}}
            </div>
            <div ng-if="item.status==3" layout="column" layout-align="center center" ng-repeat="item in line.items" class="item" ng-drop="true" ng-drop-success="pl.onDropOnProduceCell($data,line,$index,$event)" >
              <div ng-drag="true" ng-if="item.value!=''" class="productItem" ng-drag-data="{source: 'Product', productLine: line, index: $index}" >
                {{item.value}}
              </div>
            </div>
          </div>
          <md-menu>
            <!-- 生产线的产品和类型 -->
            <md-button class="lineMark" aria-label="line mark" ng-click="pl.onMarkClicked(0,$index,$mdMenu,$event)">
              {{line.product?line.product.CP_Name+line.code.CPL_Name:'无'}}
            </md-button>
            <md-menu-content width="2">
              <md-menu-item ng-repeat="processItem in pl.lineProcessItems[0][$index]">
                <md-button ng-click="pl.onLineProcess(processItem,$event)">
                  {{processItem.name}}
                </md-button>
              </md-menu-item>
            </md-menu-content>
          </md-menu>

          <div class="lineValue" ng-drag="true" ng-drag-data="{source: 'LineValue', productLine: line}" >{{line.equity}}M</div>
          <div class="lineNumber">{{$index+1}}</div>
        </div>
      </div>
    </div>

    <div layout="column" layout-align="space-between" flex id="produceRegionRight" class="right">
      <md-menu>
        <div layout="row" layout-align="center center" class="titleRow">
          <div layout="row" ng-click="pl.onFactoryClicked(1,$mdMenu,$event)" class="factory">{{pl.workshops[1].code.CW_Name}} 
            <div class="factoryValue">{{pl.workshops[1].mark}}</div>
          </div>
        </div>

        <md-menu-content width="2">
          <md-menu-item ng-repeat="processItem in pl.factoryProcessItems[1]">
            <md-button ng-click="pl.onFactoryProcess(1,processItem,$event)">
              {{processItem.name}}
            </md-button>
          </md-menu-item>
        </md-menu-content>

      </md-menu>

      <div layout="row" flex layout-align="space-between">
        <div ng-repeat="line in pl.workshops[1].productLines" layout="column" layout-align="space-between center" class="columnContainer">
          <div flex layout="column" layout-align="end center" class="productLine">
            <div ng-if="item.status==0||item.status==1" ng-repeat="item in line.items" class="circleItem">
              {{item.value}}
            </div>
            <div ng-if="item.status==2" ng-repeat="item in line.items" class="item" ng-drop="true" ng-drop-success="pl.onDropOnPrepareCell($data,line,$index,$event)">
              {{item.value}}
            </div>
            <div ng-if="item.status==3" layout="column" layout-align="center center" ng-repeat="item in line.items" class="item" ng-drop="true" ng-drop-success="pl.onDropOnProduceCell($data,line,$index,$event)" >
              <div ng-drag="true" ng-if="item.value!=''" class="productItem" ng-drag-data="{source: 'Product', productLine: line, index: $index}" >
                {{item.value}}
              </div>
            </div>
          </div>

          <md-menu>
            <md-button class="lineMark" aria-label="line mark" ng-click="pl.onMarkClicked(1,$index,$mdMenu,$event)">
              {{line.product?line.product.CP_Name+line.code.CPL_Name:'无'}}
            </md-button>
            <md-menu-content width="2">
              <md-menu-item ng-repeat="processItem in pl.lineProcessItems[1][$index]">
                <md-button ng-click="pl.onLineProcess(processItem,$event)">
                  {{processItem.name}}
                </md-button>
              </md-menu-item>
            </md-menu-content>
          </md-menu>

          <div class="lineValue" ng-drag="true" ng-drag-data="{source: 'LineValue', productLine: line}">{{line.equity}}M</div>
          <div class="lineNumber">{{pl.workshops[0].productLines.length+$index+1}}</div>
        </div>
      </div>
    </div>
  </div>

  <div layout="row" layout-align="center">
    <img class="titleText" src="assets/produce/title.png"></img>
  </div>

</div>