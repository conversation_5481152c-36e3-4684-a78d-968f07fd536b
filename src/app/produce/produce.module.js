'use strict';

angular
    .module('entrepApp.produce', ['ngMaterial', 'ngDraggable', 'entrepApp.core'])
    .controller('ProduceController', [
      'appCache', 'UserService', '$mdPanel', '$rootScope', '$log', ProduceController
    ])
    .controller('BuyRentFactoryController', [
      'appCache', 'UserService', '$rootScope', BuyRentFactoryController
    ])
    .controller('NewLineController', [
      'appCache', 'UserService', '$rootScope', NewLineController
    ])
    .controller('ProduceConfirmController', [
      'appCache', 'UserService', '$log', ProduceConfirmController
    ])
    .controller('LineTransferController', [
      'appCache', 'UserService', '$log', LineTransferController
    ])
    .controller('AttendOrderController', [
      'appCache', 'UserService', '$mdPanel', '$timeout', '$log', AttendOrderController
    ])
    .controller('OrderConfirmController', [
      'appCache', 'UserService', '$log', OrderConfirmController
    ])
    .controller('SystemConfirmController', [
      'appCache', 'UserService', '$log', SystemConfirmController
    ]);

function ProduceController(appCache, UserService, $mdPanel, $rootScope, $log) {
  var self = this;

  self.appCache = appCache;
  self.workshops = appCache.calculatedParams.workshops;
  self.system = appCache.system;
  self.moveWorkshops = appCache.moveState.workshops;
  self.moveProducts = appCache.moveState.products;

  self.factoryProcessItems = [];
  self.lineProcessItems = [];

  self.onFactoryClicked = onFactoryClicked;
  self.onMarkClicked = onMarkClicked;
  self.onFactoryProcess = onFactoryProcess;
  self.onLineProcess = onLineProcess;
  self.onDropOnPrepareCell = onDropOnPrepareCell;
  self.onDropOnProduceCell = onDropOnProduceCell; 

  //listen to events of attend order, to run the dialog
  $rootScope.$on('event:attendOrder', function(event, data) {
    if (appCache.showProduceCover) {
      return;
    }
    // show the confirm dialog
    var dlg = {
      controller: 'AttendOrderController as pl',
      templateUrl: 'app/produce/attendOrder.tmpl.html',
      attachTo: angular.element(document.querySelector('.produceContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'produceAttendOrderPanel',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {}
    };

    appCache.showProduceCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  });

  $rootScope.$on('warn:notice', function (event, data) {
    if (appCache.showSystemConfirmCover) {
      return;
    }
    // show the confirm dialog
    var dlg = {
      controller: 'SystemConfirmController as pl',
      templateUrl: 'app/produce/SystemConfirm.tmpl.html',
      attachTo: angular.element(document.querySelector('.systemConfirmContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        title: data.title,
        content: data.content,
      }
    };

    appCache.showSystemConfirmCover = true;
    $mdPanel.open(dlg).then(function (mdPanelRef) {
      // nothing
    });
  });

  // show the buy or rent factory dialog
  function onFactoryClicked(wsIndex, $mdMenu, ev) {
    var currentWorkshop = self.workshops[wsIndex];
    if (!currentWorkshop.ws) {
      if (!UserService.checkPermission('Buy_Rent_Workshop')) {
        return;
      }

      var dlg = {
        controller: 'BuyRentFactoryController as pl',
        templateUrl: 'app/produce/buyRentFactory.tmpl.html',
        attachTo: angular.element(document.querySelector('.produceContainerCover')),
        position: $mdPanel.newPanelPosition().absolute().center(),
        panelClass: 'entrepPanelClass',
        trapFocus: true,
        zIndex: 1000,
        clickOutsideToClose: false,
        escapeToClose: false,
        focusOnOpen: true,
        locals: {
          currentWorkshop,
          newFactory: {
            W_CWID: currentWorkshop.code.CW_ID,
            W_Surplus_Capacity: currentWorkshop.code.CW_Capacity
          }
        }
      };

      appCache.showProduceCover = true;
      $mdPanel.open(dlg).then(function(mdPanelRef) {
        // nothing
      });
    } else {
      _doUpdateFactoryProcessItems();
      if (self.factoryProcessItems[wsIndex] && self.factoryProcessItems[wsIndex].length > 0) {
        $mdMenu.open(ev);
      }
    }
  }

  function onMarkClicked(wsIndex, lineIndex, $mdMenu, ev) {
    var currentWorkshop = self.workshops[wsIndex];
    var currentProductLine = currentWorkshop.productLines[lineIndex];
    if (!currentWorkshop.ws) {
      return;
    }
    if (!currentProductLine.pl || !currentProductLine.pl.PL_Finish_Date) {
      if (!UserService.checkPermission('Invest_Product_Line')) {
        return;
      }

      if (!currentProductLine.pl) {
        var dlg = {
          controller: 'NewLineController as pl',
          templateUrl: 'app/produce/newLine.tmpl.html',
          attachTo: angular.element(document.querySelector('.produceContainerCover')),
          position: $mdPanel.newPanelPosition().absolute().center(),
          panelClass: 'entrepPanelClass',
          trapFocus: true,
          zIndex: 1000,
          clickOutsideToClose: false,
          escapeToClose: false,
          focusOnOpen: true,
          locals: {
            PL_ID: lineIndex + 1,
            PL_WID: wsIndex + 1
          }
        };
        if (appCache.showFinanceCover) {
          return;
        }
        appCache.showProduceCover = true;
        $mdPanel.open(dlg).then(function(mdPanelRef) {
          // nothing
        });  
      } else if (currentProductLine.pl.PL_Invest_Date != appCache.user.date) {
        var productLineCode = self.system.codes.productLines[currentProductLine.pl.PL_CPLID - 1];
        var title = '申请投资生产线';
        var content = '投资生产线费用: ' + productLineCode.CPL_Buy_Fee + 'M';

        $rootScope.$broadcast('event:paymentRequired', {
          title,
          content,
          request: {
            userID: appCache.userID,
            action: 'Invest_Product_Line',
            params: {
              PL_ID: lineIndex + 1,
              PL_WID: wsIndex + 1
            }
          }
        });
      }
    } else {
      _doUpdateLineProcessItems();
      if (self.lineProcessItems[wsIndex] && self.lineProcessItems[wsIndex][lineIndex] &&
          self.lineProcessItems[wsIndex][lineIndex].length > 0) {
        $mdMenu.open(ev);
      }
    }
  }

  function onFactoryProcess(wsIndex, item, ev) {
    var currentWorkshop = self.workshops[wsIndex];

    if (item.id == 'Buy') {
      var title = '申请厂房租转买';
      var content = '购买厂房费用: ' + currentWorkshop.code.CW_Buy_Fee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Workshop_Process',
          params: {
            ID: wsIndex + 1,
            operation: item.id
          }
        }
      });
    } else {
      var dlg = {
        controller: 'ProduceConfirmController as pl',
        templateUrl: 'app/produce/produceConfirm.tmpl.html',
        attachTo: angular.element(document.querySelector('.produceContainerCover')),
        position: $mdPanel.newPanelPosition().absolute().center(),
        panelClass: 'entrepPanelClass',
        trapFocus: true,
        zIndex: 1000,
        clickOutsideToClose: false,
        escapeToClose: false,
        focusOnOpen: true,
        locals: {
          title: '厂房' + item.name,
          content: '确认厂房' + item.name,
          request: {
            userID: appCache.userID,
            action: item.id == 'Discount' ? 'Workshop_Discount' : 'Workshop_Process',
            params: {
              ID: wsIndex + 1,
              operation: item.id
            }
          }
        }
      };

      appCache.showProduceCover = true;
      $mdPanel.open(dlg).then(function(mdPanelRef) {
        // nothing
      });
    }
  }

  function onLineProcess(item, ev) {
    var dlg = null;
    if (item.id == 'Transfer') {
      var productLine = self.workshops[item.wsIndex].productLines[item.lineIndex];

      dlg = {
        controller: 'LineTransferController as pl',
        templateUrl: 'app/produce/lineTransfer.tmpl.html',
        attachTo: angular.element(document.querySelector('.produceContainerCover')),
        position: $mdPanel.newPanelPosition().absolute().center(),
        panelClass: 'entrepPanelClass',
        trapFocus: true,
        zIndex: 1000,
        clickOutsideToClose: false,
        escapeToClose: false,
        focusOnOpen: true,
        locals: {
          currentProductLine: productLine,
          request: {
            userID: appCache.userID,
            action: 'New_Transfer',
            params: {
              PL_ID: item.lineIndex + 1,
              PL_WID: item.wsIndex + 1
            }
          }
        }
      };
    } else {
      dlg = {
        controller: 'ProduceConfirmController as pl',
        templateUrl: 'app/produce/produceConfirm.tmpl.html',
        attachTo: angular.element(document.querySelector('.produceContainerCover')),
        position: $mdPanel.newPanelPosition().absolute().center(),
        panelClass: 'entrepPanelClass',
        trapFocus: true,
        zIndex: 1000,
        clickOutsideToClose: false,
        escapeToClose: false,
        focusOnOpen: true,
        locals: {
          title: '变卖生产线',
          content: '确认变卖生产线',
          request: {
            userID: appCache.userID,
            action: 'Sell_Product_Line',
            params: {
              PL_ID: item.lineIndex + 1,
              PL_WID: item.wsIndex + 1
            }
          }
        }
      };
    }

    appCache.showProduceCover = true;
    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function onDropOnPrepareCell(data, productLine, itemIndex, ev) {
    if (!UserService.checkPermission('New_Produce')) {
      return;
    }
    if (data.source != 'MaterialInventory') {
      return;
    }
    if (itemIndex != productLine.items.length - 1) {
      return;
    }
    // 财务确认时 不能操作
    if(appCache.showFinanceCover){
      return;
    }
    var productID = productLine.pl.PL_CPID;
    var productDevelop = appCache.calculatedParams.productDevelops[productID - 1];
    if (!productDevelop.DP_Finish_Date) {
      return;
    }
    var materialNum = 0;
    for (var index = 0; index < productLine.productBOMs.length; ++index) {
      var productBOM = productLine.productBOMs[index];
      if (productBOM.CPB_CMID == data.index + 1) {
        materialNum = productBOM.CPB_Num;
        break;
      }
    }
    if (materialNum == 0) {
      return;
    }

    var materialsInventory = appCache.calculatedParams.materialsInventory; 
    var materialInventory = materialsInventory[data.index];
    if (materialInventory.num < materialNum) {
      return;
    }

    var wsIndex = productLine.pl.PL_WID - 1;
    var plIndex = productLine.pl.PL_ID - 1;
    if (!self.moveWorkshops[wsIndex]) {
      self.moveWorkshops[wsIndex] = [];
    }
    if (!self.moveWorkshops[wsIndex][plIndex]) {
      self.moveWorkshops[wsIndex][plIndex] = [];
    }
    self.moveWorkshops[wsIndex][plIndex][data.index] = true;
    UserService.updateCalculatedParams();
    
    var materialPrepared = true; 
    for (var index = 0; index < productLine.productBOMs.length; ++index) {
      var productBOM = productLine.productBOMs[index];
      var materialIndex = productBOM.CPB_CMID - 1;
      if (!self.moveWorkshops[wsIndex][plIndex][materialIndex]) {
        materialPrepared = false;
        break;
      }
    }
    if (materialPrepared) {
      self.moveWorkshops[wsIndex][plIndex] = [];

      var title = '申请下一批生产';
      var content = '申请下一批生产加工费用: ' + productLine.product.CP_Processing_Fee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'New_Produce',
          params: {
            PL_ID: productLine.pl.PL_ID,
            PL_WID: productLine.pl.PL_WID
          }
        }
      });
    }
  }

  function onDropOnProduceCell(data, productLine, itemIndex, ev) {
    if (!UserService.checkPermission('Update_Produce')) {
      return;
    }
    if (data.source != 'Product') {
      return;
    }
    if (data.productLine != productLine) {
      return;
    }
    if (data.index != itemIndex + 1) {
      return;
    }

    var wsIndex = productLine.pl.PL_WID - 1;
    var plIndex = productLine.pl.PL_ID - 1;
    if (!self.moveProducts[wsIndex]) {
      self.moveProducts[wsIndex] = [];
    }
    self.moveProducts[wsIndex][plIndex] = true;
    UserService.updateCalculatedParams();

    _checkProductMove();
  }

  // help to update factory process items
  function _doUpdateFactoryProcessItems() {
    var allowProcess = UserService.checkPermission('Workshop_Process');
    var allowDiscount = UserService.checkPermission('Workshop_Discount');

    for (var index = 0; index < self.workshops.length; ++index) {
      var workshop = self.workshops[index];
      var items = [];
      self.factoryProcessItems[index] = items;

      if (!workshop.ws) continue;
      // check whether there are product lines
      var hasProductLine = false;
      for (var k = 0; k < workshop.productLines.length; ++k) {
        var productLine = workshop.productLines[k];
        if (productLine.pl) {
          hasProductLine = true;
          break;
        }
      }

      if (allowProcess) {
        if (workshop.ws.W_Status == 'Buy') {
          if (!hasProductLine) {
            items.push({ name: '出售', id: 'Sell' });
          }
          items.push({ name: '买转租', id: 'Rent' });
        } else {
          if (workshop.ws.W_Pay_Date < appCache.user.date && 
              workshop.ws.W_Pay_Date % 10 == appCache.user.date % 10) {
            items.push({ name: '租转买', id: 'Buy' });
            if (!hasProductLine) {
              items.push({ name: '退租', id: 'Sell' });
            }
          }
        }
      }
      if (allowDiscount) {
        if (workshop.ws.W_Status == 'Buy') {
          items.push({ name: '贴现', id: 'Discount' });
        }
      }
    }
  }

  // help to update product line process items
  function _doUpdateLineProcessItems() {
    var allowSell = UserService.checkPermission('Sell_Product_Line');
    var allowTransfer = UserService.checkPermission('New_Transfer');

    for (var index = 0; index < self.workshops.length; ++index) {
      var workshop = self.workshops[index];
      var items = [];
      self.lineProcessItems[index] = items;

      for (var k = 0; k < workshop.productLines.length; ++k) {
        var productLine = workshop.productLines[k];
        items[k] = [];
        if (productLine.pl && productLine.pl.PL_Finish_Date && !productLine.pl.PL_Product_Add_Date) {
          if (allowTransfer) {
            items[k].push({ name: '转产', id: 'Transfer', wsIndex: index, lineIndex: k });
          }
          if (allowSell) {
            items[k].push({ name: '变卖', id: 'Sell', wsIndex: index, lineIndex: k });
          }
        }
      }
    }
  }

  function _checkProductMove() {
    if (!appCache.moveState.productMoveCompleted) {
      return;
    }
    self.moveProducts.splice(0, self.moveProducts.length);
    UserService.requestAction({
      userID: appCache.userID,
      action: 'Update_Produce',
      params: {}
    }, function() {
      $log.info('Request completed.');
    }, function(e) {
      $log.error(e.data);
    });
  }
}

function BuyRentFactoryController(appCache, UserService, $rootScope) {
  var self = this;

  self.confirmBuyRentFactory = confirmBuyRentFactory;
  self.cancelBuyRentFactory = cancelBuyRentFactory;

  function cancelBuyRentFactory() {
    self.mdPanelRef.close().then(function() {
      appCache.showProduceCover = false;
    });
  };

  function confirmBuyRentFactory() {
    self.mdPanelRef.close().then(function() {
      var title = '申请租用厂房';
      var content = '租用厂房费用: ' + self.currentWorkshop.code.CW_Rent_Fee + 'M';
      if (self.newFactory.W_Status == 'Buy') {
        title = '申请购买厂房';
        content = '购买厂房费用: ' + self.currentWorkshop.code.CW_Buy_Fee + 'M';
      }

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Buy_Rent_Workshop',
          params: self.newFactory
        } 
      });

      appCache.showProduceCover = false;
    });
  };
}

function NewLineController(appCache, UserService, $rootScope) {
  var self = this;

  self.system = appCache.system;
  self.newLineId = null;
  self.newLineProduct = null;

  self.confirmNewLine = confirmNewLine;
  self.cancelNewLine = cancelNewLine;

  function cancelNewLine() {
    self.mdPanelRef.close().then(function() {
      appCache.showProduceCover = false;
    });
  };

  function confirmNewLine() {
    self.mdPanelRef.close().then(function() {
      var productLineCode = self.system.codes.productLines[self.newLineId - 1];

      var title = '申请投资生产线';
      var content = '投资生产线费用: ' + productLineCode.CPL_Buy_Fee + 'M';

      $rootScope.$broadcast('event:paymentRequired', {
        title,
        content,
        request: {
          userID: appCache.userID,
          action: 'Invest_Product_Line',
          params: {
            PL_ID: self.PL_ID,
            PL_WID: self.PL_WID,
            PL_CPLID: parseInt(self.newLineId),
            PL_CPID: parseInt(self.newLineProduct),
            PL_Remain_Date: productLineCode.CPL_Install_Date - 1,
            PL_Finish_Date: productLineCode.CPL_Install_Date == 1 ? appCache.user.date : null,
            PL_Add_Date: appCache.user.date,
            PL_Invest: productLineCode.CPL_Buy_Fee,
            PL_Invest_Date: appCache.user.date,
            PL_Dep_Total: 0
          }
        }
      });

      appCache.showProduceCover = false;
    });
  };
}

function ProduceConfirmController(appCache, UserService, $log) {
  var self = this;

  self.confirm = confirm;
  self.cancel = cancel;

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showProduceCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      
      UserService.requestAction(self.request, function() {
        // nothing
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showProduceCover = false;
    });
  };
}

function LineTransferController(appCache, UserService, $log) {
  var self = this;

  self.targetProductID = null;
  self.targetProducts = [];

  self.confirm = confirm;
  self.cancel = cancel;

  for (var index = 0; index < appCache.system.codes.products.length; ++index) {
    var productCode = appCache.system.codes.products[index];
    if (productCode.CP_ID != self.currentProductLine.pl.PL_CPID) {
      self.targetProducts.push(productCode);
    }
  }

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showProduceCover = false;
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      
      self.request.params.PL_CPID = parseInt(self.targetProductID);
      UserService.requestAction(self.request, function() {
        // nothing
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showProduceCover = false;
    });
  };
}

function OrderConfirmController(appCache, UserService, $log) {
  var self = this;

  self.confirm = confirm;
  self.cancel = cancel;

  function cancel() {
    self.mdPanelRef.close().then(function() {
      // nothing
    });
  };

  function confirm() {
    self.mdPanelRef.close().then(function() {
      
      UserService.selectOrder(self.orderCode.CO_ID, 
        function() {
          // nothing
        }, function(e) {
          $log.error(e.data);
        }
      );

    });
  };
}

function AttendOrderController(appCache, UserService, $mdPanel, $timeout, $log) {
  var self = this;

  self.system = appCache.system;
  self.user = appCache.user;

  self.status = -1;
  self.rounds = null;
  self.orderReady = null;
  self.systemYear = 0;

  self.teamCouldSelect = false;
  self.couldSelectISO = false;

  self.ISO1Status = false;
  self.ISO2Status = false;

  self.confirmOrderFinished = confirmOrderFinished;
  self.cancel = cancel;
  self.onOrderClicked = onOrderClicked;

  var _scheduleWaiting = false;

  _checkISOStatus();

  _syncOrderStatus();

  function cancel() {
    self.mdPanelRef.close().then(function() {
      appCache.showProduceCover = false;
    });
  };

  function confirmOrderFinished() {
    self.mdPanelRef.close().then(function() {
      
      UserService.requestAction({
        userID: appCache.userID,
        action: 'Attend_Order',
        params: {}
      }, function() {
        // nothing
      }, function(e) {
        $log.error(e.data);
      });

      appCache.showProduceCover = false;
    });
  };

  function onOrderClicked(index) {
    if (index < 0) {
      UserService.selectOrder(null, 
        function() {
          // nothing
        }, function(e) {
          $log.error(e.data);
        }
      );
      return;
    }
    
    const orderCode = self.rounds[0].orders[index];

    if (!self.teamCouldSelect || ((orderCode.CO_ISO == 1 && !self.ISO1Status)) || ((orderCode.CO_ISO == 2 && !self.ISO2Status))) {
      return;
    }

    var dlg = {
      controller: 'OrderConfirmController as pl',
      templateUrl: 'app/produce/orderConfirm.tmpl.html',
      attachTo: angular.element(document.querySelector('.produceContainerCover')),
      position: $mdPanel.newPanelPosition().absolute().center(),
      panelClass: 'entrepPanelClass',
      trapFocus: true,
      zIndex: 1000,
      clickOutsideToClose: false,
      escapeToClose: false,
      focusOnOpen: true,
      locals: {
        orderCode,
      }
    };

    $mdPanel.open(dlg).then(function(mdPanelRef) {
      // nothing
    });
  }

  function _syncOrderStatus() {
    if (!appCache.showProduceCover) return;
    
    UserService.getOrderStatus(
      function(data) {
        self.status = data.status;
        self.rounds = data.rounds;
        self.orderReady = data.orderReady;
        self.systemYear = data.systemYear;

        self.teamCouldSelect = false;

        if (Math.floor(self.user.date / 10) < self.system.systemYear) {
          self.status = 1;
        }
        if (self.rounds && self.rounds[0] && self.rounds[0].orders) {
          self.rounds[0].orders.sort(function(lhs, rhs) {
            if (lhs.CO_Total != rhs.CO_Total) {
              return rhs.CO_Total - lhs.CO_Total;
            }
            if (lhs.CO_Num != rhs.CO_Num) {
              return rhs.CO_Num - lhs.CO_Num;
            }
            return 0;
          })
        }
        if (self.status == 2) {
          for (var i = 0; i < self.rounds[0].teams.length; ++i) {
            var team = self.rounds[0].teams[i];
            if (team.userID == self.user.userID && team.countdown) {
              self.teamCouldSelect = true;
              self.couldSelectISO = team.hasISO;
              break;
            }
          }
        }

        if (self.status != 1) {
          _scheduleNextStatusSync();
        }
      }, function(e) {
        $log.error(e.data);
      }
    );
  }

  // schedule next poll
  function _scheduleNextStatusSync() {
    if (_scheduleWaiting) {
      return;
    }
    _scheduleWaiting = true;
    $timeout(function() {
      _syncOrderStatus();
      _scheduleWaiting = false;
    }, 1000);
  }

  function _checkISOStatus() {
    if(self.user.developISOs.length>0){
      self.user.developISOs.forEach(iso=>{
        if(iso.DI_CIID == 1 && iso.DI_Remain_Date == 0){
          self.ISO1Status = true;
        } else if(iso.DI_CIID == 2 && iso.DI_Remain_Date == 0){
          self.ISO2Status = true;
        }
      })
    }
  }
}

function SystemConfirmController(appCache, UserService, $log) {
  var self = this;

  self.confirm = confirm;

  function confirm() {
    self.mdPanelRef.close().then(function() {
      appCache.showSystemConfirmCover = false;
    });
  };
}
