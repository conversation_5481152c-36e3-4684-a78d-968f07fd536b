<div layout="row" layout-align="center" layout-fill class="entrepRegister" ng-controller="RegisterController as rl">
  <md-content>

  <div class="panel panel-success">
    <div class="panel-heading">
      用户登记
    </div>
    <div class="panel-body">
      <form class="md-padding" name="registerForm">
        <div layout="row">
          <div flex layout="column" class="rowLeft">
            <md-input-container class="md-block">
              <input ng-model="rl.user.username" name="input" type="text" placeholder="用户名" required readonly>
            </md-input-container>
            <md-input-container class="md-block">
              <input ng-model="rl.user.password" name="input" type="text" placeholder="重置密码" ng-virtual-keyboard required>
            </md-input-container>
            <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_School" name="input" type="text" placeholder="所属学校" ng-virtual-keyboard required>
            </md-input-container>
            <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_Company" name="input" type="text" rows="2" placeholder="公司名称" ng-virtual-keyboard required>
              </input>
            </md-input-container>
            <md-input-container class="md-block">
              <textarea ng-model="rl.user.team.M_Company_Info" name="input" type="text" placeholder="公司简介" rows="5" ng-virtual-keyboard required>
              </textarea>
            </md-input-container>
          </div>
          <div flex layout="column">
            <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_CEO" name="input" type="text" placeholder="总裁" ng-virtual-keyboard required>
            </md-input-container>
             <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_CFO" name="input" type="text" placeholder="财务总监" ng-virtual-keyboard required>
            </md-input-container>
             <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_CPO" name="input" type="text" placeholder="生产总监" ng-virtual-keyboard required>
            </md-input-container>
             <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_CMO" name="input" type="text" placeholder="销营总监" ng-virtual-keyboard required>
            </md-input-container>
             <md-input-container class="md-block">
              <input ng-model="rl.user.team.M_CLO" name="input" type="text" placeholder="采购总监" ng-virtual-keyboard required>
            </md-input-container>
          </div>
        </div>
        

        <span class="error" ng-show="rl.registerFailed">{{rl.registerError}}</span>
        <div>
          <md-button ng-disabled="!registerForm.$valid" ng-click="rl.register()" class="md-raised md-primary action">登记确认</md-button>
        </div>

      </form>
    </div>
  </div>

  </md-content>
</div>