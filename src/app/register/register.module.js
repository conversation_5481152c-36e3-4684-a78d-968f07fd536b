'use strict';

angular
    .module('entrepApp.register', ['ngMaterial', 'entrepApp.core'])
    .controller('RegisterController', [
      'appCache', 'UserService', '$log', RegisterController
    ]);

function RegisterController(appCache, UserService, $log, ) {
  var self = this;
  self.user = { userID: appCache.userID, username: appCache.username };
  self.register = register;

  function register() {
    self.registerFailed = false;
    UserService.register(self.user, function() {
      $log.info('registered!');
    }, function(e) {
      self.registerFailed = true;
      self.registerError = e.data;
    });
  }
}