<!doctype html>
<html lang="en">
  <head>
    <title>多点触控企业经营模拟沙盘</title>
    
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">

    <link rel="stylesheet" href="../node_modules/bootstrap/dist/css/bootstrap.css" />
    <link rel="stylesheet" href="../node_modules/angular-material/angular-material.css"/>
    <link rel="stylesheet" href="../node_modules/angular-ui-bootstrap/dist/ui-bootstrap-csp.css"/>
    <link rel="stylesheet" href="../node_modules/angular-virtual-keyboard/release/angular-virtual-keyboard.css">
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="app/finance/finance.css" />
    <link rel="stylesheet" href="app/info/info.css" />
    <link rel="stylesheet" href="app/login/login.css" />
    <link rel="stylesheet" href="app/logistic/logistic.css" />
    <link rel="stylesheet" href="app/market/market.css" />
    <link rel="stylesheet" href="app/produce/produce.css" />
    <link rel="stylesheet" href="app/register/register.css" />

    <style type="text/css">
        /**
         * Hide when Angular is not yet loaded and initialized
         */
        [ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
          display: none !important;
        }
    </style>
    
  </head>
  <body ng-app="entrepApp" class="entrepApp" ng-controller="AppController as al" ng-cloak>
    <div ng-if="!al.appCache.token" layout-fill ng-include="'app/login/login.html'"></div>
    <div ng-if="al.appCache.user.status=='New'" layout-fill ng-include="'app/register/register.html'"></div>
    <div ng-if="al.appCache.token && al.appCache.user.status!='New'" layout-fill ng-controller="MainController as ml" class="mainContainer" ng-cloak>
      <div ng-if="!ml.userLoading" layout-fill>
        <div class="infoContainer" ng-include="'app/info/info.html'"></div>
        <div class="marketContainer" ng-include="'app/market/market.html'"></div>
        <div class="financeContainer" ng-include="'app/finance/finance.html'"></div>
        <div class="produceContainer" ng-include="'app/produce/produce.html'"></div>
        <div class="logisticContainer" ng-include="'app/logistic/logistic.html'"></div>

        <div ng-show="ml.appCache.showInfoCover" class="infoContainerCover"></div>
        <div ng-show="ml.appCache.showInfoGuideCover" class="infoContainerGuideCover"></div>
        <div ng-show="ml.appCache.showMarketCover" class="marketContainerCover"></div>
        <div ng-show="ml.appCache.showFinanceCover" class="financeContainerCover"></div>
        <div ng-show="ml.appCache.showProduceCover" class="produceContainerCover"></div>
        <div ng-show="ml.appCache.showLogisticCover" class="logisticContainerCover"></div> 
        <div ng-show="ml.appCache.showSystemConfirmCover" class="systemConfirmContainerCover"></div> 
      </div>

      <div ng-if="ml.userLoading" layout="row" class="loadingCover" layout-fill layout-align="center center">
        <md-progress-circular md-mode="indeterminate"></md-progress-circular>
      </div>
    </div>

    <script src="../node_modules/hammerjs/hammer.js"></script>
    <script src="../node_modules/angular/angular.js"></script>
    <script src="../node_modules/ngstorage/ngStorage.js"></script>
    <script src="../node_modules/angular-resource/angular-resource.js"></script>
    <script src="../node_modules/angular-animate/angular-animate.js"></script>
    <script src="../node_modules/angular-aria/angular-aria.js"></script>
    <script src="../node_modules/angular-material/angular-material.js"></script>
    <script src="../node_modules/angular-ui-bootstrap/dist/ui-bootstrap-tpls.js"></script>
    <script src="../node_modules/angular-virtual-keyboard/release/angular-virtual-keyboard.js"></script>
    <script src="../third_party/angular.hammer.js"></script>
    <script src="../third_party/ngDraggable/ngDraggable.js"></script>
    <script src="app/core/core.module.js"></script>
    <script src="app/core/disk-cache.service.js"></script>
    <script src="app/core/hm.dir.js"></script>
    <script src="app/core/charts.dir.js"></script>
    <script src="app/core/num-select.dir.js"></script>
    <script src="app/core/auth.service.js"></script>
    <script src="app/core/user.service.js"></script>
    <script src="app/app.module.js"></script>
    <script src="app/login/login.module.js"></script>
    <script src="app/register/register.module.js"></script>
    <script src="app/info/info.module.js"></script>
    <script src="app/produce/produce.module.js"></script>
    <script src="app/market/market.module.js"></script>
    <script src="app/finance/finance.module.js"></script>
    <script src="app/logistic/logistic.module.js"></script>

  </body>

</html>