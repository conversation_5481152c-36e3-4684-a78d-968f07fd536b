
body {
  font: caption;
  font-family: '黑体';
  user-select: none;
}

@font-face {
  font-family:DFFangYuanW7;
  src: url(assets/font/W7-GB.TTC);
}

.md-button.md-small {
  padding: 4px;
}
.entrepApp .error {
  color: red;
}

.entrepApp .s48 {
  width: 96px;
  height: 96px;
  color: red;
}

.mainContainer {
  width: 100%;
  height: 100%;
  background: rgba(177,225,241,1);
}

.mainContainer .loadingCover {
  background: grey;
  position: absolute;
  width: 100%;
  height: 100%;
}

.mainContainer .infoContainer {
  position:absolute;
  width: 1055px;
  height: 345px;
  left: 350px;
  top: 2px;
  transform: rotate(90deg);
  transform-origin:0% 0%;

  background: #FDEFC2;
  border: 5px solid #D9A473;
  box-shadow: inset 0 1px 49px 24px #E0C883;
  border-radius: 38px;
}

.mainContainer .infoContainerCover, .mainContainer .infoContainerGuideCover {
  position:absolute;
  width: 1055px;
  height: 345px;
  left: 350px;
  transform: rotate(90deg);
  transform-origin:0% 0%;
}


.mainContainer .marketContainer {
  position:absolute;
  width: 1055px;
  height: 345px;
  right: 345px;
  top: 2px;
  transform: rotate(-90deg);
  transform-origin:right top;

  background: #FDEFC2;
  border: 5px solid #D9A473;
  box-shadow: inset 0 1px 49px 24px #E0C883;
  border-radius: 38px;
}

.mainContainer .marketContainerCover {
  position:absolute;
  width: 1055px;
  height: 345px;
  right: 345px;
  top: 2px;
  transform: rotate(-90deg);
  transform-origin:right top;

  background: #FDEFC2;
  border: 5px solid #D9A473;
  box-shadow: inset 0 1px 49px 24px #E0C883;
  border-radius: 38px;
}

.mainContainer .produceContainer {
  position: absolute;
  left: 352px;
  top: 330px;
  right: 350px;
  height: 374px;

  box-sizing:border-box;
  background: #FDEFC2;
  border: 5px solid #D9A473;
  box-shadow: inset 0 1px 49px 24px #E0C883;
  border-radius: 38px;
}

.mainContainer .produceContainerCover, .systemConfirmContainerCover {
  position: absolute;
  left: 352px;
  top: 330px;
  right: 350px;
  height: 374px;
}

.mainContainer .logisticContainer {
  position: absolute;
  left: 352px;
  right: 350px;
  height: 340px;
  top: 710px;

  box-sizing:border-box;
  background: #FDEFC2;
  border: 5px solid #D9A473;
  box-shadow: inset 0 1px 49px 24px #E0C883;
  border-radius: 38px;
}

.mainContainer .logisticContainerCover {
  position: absolute;
  left: 352px;
  right: 350px;
  height: 340px;
  top: 710px;
}

.mainContainer .financeContainer {
  position: absolute;
  height: 320px;
  left: 352px;
  right: 350px;
  top: 5px;
  transform:rotate(180deg);
  -ms-transform:rotate(180deg); /* IE 9 */
  -moz-transform:rotate(180deg);  /* Firefox */
  -webkit-transform:rotate(180deg); /* Safari 和 Chrome */
  -o-transform:rotate(180deg);  /* Opera */
  
  box-sizing:border-box;
  background: #FDEFC2;
  border: 5px solid #D9A473;
  box-shadow: inset 0 1px 49px 24px #E0C883;
  border-radius: 38px;
}

.mainContainer .financeContainerCover {
  position: absolute;
  height: 320px;
  left: 352px;
  right: 350px;
  top: 5px;
  transform:rotate(180deg);
  -ms-transform:rotate(180deg); /* IE 9 */
  -moz-transform:rotate(180deg);  /* Firefox */
  -webkit-transform:rotate(180deg); /* Safari 和 Chrome */
  -o-transform:rotate(180deg);  /* Opera */
}

.mainContainer .regionTitle {
  padding: 5px 10px 5px 10px;

  font-family: DFFangYuanW7;
  font-size: 22px;
  color: #FFFFFF;
  text-shadow: 0 1px 1px rgba(0,0,0,0.27);

  background-image: linear-gradient(-180deg, #9ADC3E 0%, #9CCD37 100%);
  border: 1px solid #B68F5F;
  box-shadow: 0 -1px 4px 0 rgba(169,137,33,0.32), inset 0 2px 5px 1px rgba(173,245,46,0.75);
}

.entrepPanelClass {
  position: absolute;
  left: 50%;
  top: 50%;
}

.entrepPanelClass hm-dir {
  transform: translate(-50%, -50%);
}

.numSelectContainer {
  position: relative;
  width: 100%;
  background: black;
  text-align: center;
  height: 96px;
  overflow: hidden;
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.numSelectElemContainer {
  position: relative;
  height: 100%;
}

.numSelectItem {
  position: absolute;
  width: 45px;
  top: 32px;
  padding-left: 10px;
  padding-right: 10px;
}

.numSelectValueItem {
  line-height: 32px;
  height: 32px;
  width: 25px;
}

/*
[ng-drag] {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
[ng-drag] {
    width: 100px;
    height: 100px;
    background: rgba(255, 0, 0, 0.5);
    color: white;
    text-align: center;
    padding-top: 40px;
    display: inline-block;
    margin: 0 10px;
    cursor: move;
}*/
/*
ul.draggable-objects:after {
    display: block;
    content: "";
    clear: both;
}
.draggable-object {
    float: left;
    display: block;
}*/
/*
[ng-drag].drag-over {
    border: solid 1px red;
}
[ng-drag].dragging {
    opacity: 0.5;
}
[ng-drop] {
    background: rgba(0, 255, 0, 0.5);
    text-align: center;
    width: 600px;
    height: 200px;
    padding-top: 90px;
    display: block;
    margin: 20px auto;
    position: relative;
}*/

[ng-drag].dragging{
    opacity: 0.5;
}

[ng-drag].drag-over{
    border:solid 1px red;
}

[ng-drop].drag-enter{
  border:solid 2px red;
}