<!DOCTYPE html>
<html>
<head>
  <title>ngDraggable</title>
  <link rel="stylesheet" href="http://netdna.bootstrapcdn.com/bootswatch/2.3.1/spruce/bootstrap.min.css">
  <style>

    *{
        -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box;
    }
    [ng-drag], [ng-drag-clone]{
        -moz-user-select: -moz-none;
        -khtml-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }
    .drag-object, [ng-drag-clone], [ng-drop] [ng-drag]{
        width: 100px;
        height: 100px;
        background: rgba(255,0,0, 0.5);
        color:white;
        text-align: center;
        padding-top:40px;
        display: inline-block;
        margin:0 10px;
        cursor: move;
        position: relative;
        overflow: hidden;
    }
    .drag-object [ng-drag]{
        width: 100px;
        height: 100px;
        position: absolute;
        top:0; left:0;
        padding: 0;
        margin: 0;
    }
    [ng-drag-clone]{
        margin: 0;
    }
    [ng-drag].drag-over{
        border:solid 1px red;
    }
    [ng-drag].dragging{
        opacity: 0.5;
    }
    [ng-drop]{
        background: rgba(0,255,0, 0.5);
        text-align: center;
        width: 600px;
        height: 200px;
        padding-top:90px;
        display: block;
        margin:20px auto;
        position: relative;
    }
    [ng-drop].drag-enter{
        border:solid 5px red;
    }
    [ng-drop] span.title{
        display: block;
        position: absolute;
        top:50%;
        left:50%;
        width: 200px;
        height: 20px;
        margin-left: -100px;
        margin-top: -10px;
    }
    [ng-drop] div{
        position: relative;
        z-index: 2;
    }

  </style>
</head>
<body ng-app="ExampleApp">

  <div class="container" ng-controller="MainCtrl">

    <div class="row">
      <h1>ngDraggable Clone Example</h1>
    </div>


      <div class="drag-object" ng-repeat="obj in draggableObjects" ng-if="obj.allowClone !== false">
          {{obj.name}}
          <div ng-drag="true" ng-drag-data="obj"></div>
      </div>
      <div class="drag-object"  style="background-color: transparent; overflow: visible">
          <div ng-drag="true" class="drag-object" ng-drag-data="draggableObjects[3]">{{draggableObjects[3].name}}</div>
      </div>
      <hr/>
      <div ng-drop="true" ng-drop-success="onDropComplete1($data,$event)">
          <span class="title">Drop area #1</span>

          <div ng-repeat="obj in droppedObjects1" ng-drag="true" ng-drag-data="obj" ng-drag-success="onDragSuccess1($data,$event)">
              {{obj.name}}
          </div>

      </div>
    <hr>
  </div>


  <div ng-drag-clone="">
      {{clonedData.name}}
  </div>

  <footer style="margin-top:2000px;">footer</footer>

  <script src="http://ajax.googleapis.com/ajax/libs/angularjs/1.3.8/angular.min.js"></script>
  <script src="ngDraggable.js"></script>
  <script>
    angular.module('ExampleApp', ['ngDraggable']).
      controller('MainCtrl', function ($scope) {
        $scope.draggableObjects = [{name:'one'}, {name:'two'}, {name:'three'}, {name:'no-clone', allowClone:false}];
        $scope.droppedObjects1 = [];
        $scope.droppedObjects2= [];
        $scope.onDropComplete1=function(data,evt){
            var index = $scope.droppedObjects1.indexOf(data);
            if (index == -1)
            $scope.droppedObjects1.push(data);
        }
        $scope.onDragSuccess1=function(data,evt){
            var index = $scope.droppedObjects1.indexOf(data);
            if (index > -1) {
                $scope.droppedObjects1.splice(index, 1);
            }
        }
        $scope.onDragSuccess2=function(data,evt){
            var index = $scope.droppedObjects2.indexOf(data);
            if (index > -1) {
                $scope.droppedObjects2.splice(index, 1);
            }
        }
        $scope.onDropComplete2=function(data,evt){
            var index = $scope.droppedObjects2.indexOf(data);
            if (index == -1) {
                $scope.droppedObjects2.push(data);
            }
        }
        var inArray = function(array, obj) {
            var index = array.indexOf(obj);
        }
      });
  </script>
</body>
</html>
